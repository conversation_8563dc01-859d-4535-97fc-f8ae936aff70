# User Entity Cleanup Suggestions for Supabase Authentication

Since you're now using Supabase for authentication, some fields in your User entity might be redundant or need adjustment:

## Fields You Could Remove/Modify

### 1. Password-Related Fields (No longer needed)

```java
@JsonIgnore
@NotNull
@Size(min = 60, max = 60)
@Column(name = "password_hash", length = 60, nullable = false)
private String password; // ❌ Remove - Supabase handles passwords

@Size(max = 20)
@Column(name = "activation_key", length = 20)
@JsonIgnore
private String activationKey; // ❌ Remove - Supabase handles activation

@Size(max = 20)
@Column(name = "reset_key", length = 20)
@JsonIgnore
private String resetKey; // ❌ Remove - Supabase handles password reset

@Column(name = "reset_date")
private Instant resetDate = null; // ❌ Remove - Supabase handles reset

```

### 2. Login Field (Could be simplified)

```java
@NotNull
@Pattern(regexp = Constants.LOGIN_REGEX)
@Size(min = 1, max = 50)
@Column(length = 50, unique = true, nullable = false)
private String login; // ⚠️ Could use email as login, or remove entirely

```

### 3. Activated Field (Might be redundant)

```java
@NotNull
@Column(nullable = false)
private boolean activated = false; // ⚠️ Supabase handles user activation

```

## Fields You Should Keep

### Essential for Application Logic

- `id` - Primary key for relationships
- `email` - Primary identifier, synced from Supabase
- `firstName`, `lastName` - User profile data
- `authorities` - Application-specific roles/permissions
- `langKey`, `imageUrl` - Application preferences

### Extended Profile Data (from UserProfile merge)

- `phoneNumber`, `gender`, `dob`, `nationality` - Business-specific data
- `provider` - Track authentication provider
- `promoCodes` - Business relationships

### Audit Fields (from AbstractAuditingEntity)

- `createdBy`, `createdDate`, `lastModifiedBy`, `lastModifiedDate`

## Recommended Cleanup Actions

1. **Remove password-related fields** since Supabase handles authentication
2. **Simplify login field** - could just use email
3. **Keep activated field** but sync it from Supabase user status
4. **Add Supabase user ID field** to link local user with Supabase user

```java
// Add this field to link with Supabase
@Column(name = "supabase_user_id")
private String supabaseUserId;

```
