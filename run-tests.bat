@echo off
echo ========================================
echo Running Ferigo Backend Unit Tests
echo ========================================

echo.
echo Cleaning previous build artifacts...
call mvn clean

echo.
echo Compiling project...
call mvn compile test-compile

echo.
echo Running all unit tests...
call mvn test

echo.
echo Generating test report...
call mvn surefire-report:report

echo.
echo ========================================
echo Test execution completed!
echo ========================================
echo.
echo Test reports can be found in:
echo - target/surefire-reports/
echo - target/site/surefire-report.html
echo.

pause
