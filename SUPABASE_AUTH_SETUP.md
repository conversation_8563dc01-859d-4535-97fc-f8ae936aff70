# Supabase Authentication Setup

This application has been refactored to use Supabase authentication instead of the default JHipster JWT authentication.

## Configuration

### Environment Variables

Set the following environment variables or update the configuration files:

```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-jwt-secret
```

### Application Configuration

The Supabase configuration is defined in `application-dev.yml`:

```yaml
supabase:
  url: ${SUPABASE_URL:https://your-project.supabase.co}
  anon-key: ${SUPABASE_ANON_KEY:your-anon-key}
  service-role-key: ${SUPABASE_SERVICE_ROLE_KEY:your-service-role-key}
  jwt-secret: ${SUPABASE_JWT_SECRET:your-jwt-secret}
```

## API Endpoints

### Authentication Endpoints

- `POST /api/auth/signin` - Sign in with email and password
- `POST /api/auth/signup` - Sign up with email and password
- `POST /api/auth/signout` - Sign out current user
- `POST /api/auth/refresh` - Refresh access token

### Sign In Request

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Sign Up Request

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe"
}
```

### Refresh Token Request

```json
{
  "refreshToken": "your-refresh-token"
}
```

## Authentication Flow

1. **Sign Up/Sign In**: Users authenticate through Supabase using the `/api/auth/signin` or `/api/auth/signup` endpoints
2. **JWT Token**: Supabase returns a JWT token that contains user information
3. **User Sync**: The application automatically syncs user data from Supabase to the local database
4. **Authorization**: Subsequent requests use the Supabase JWT token for authorization

## Key Components

### SupabaseAuthService

- Handles communication with Supabase Auth API
- Manages user sign in, sign up, sign out, and token refresh
- Syncs user data between Supabase and local database

### SupabaseJwtDecoder

- Custom JWT decoder for Supabase tokens
- Validates JWT signatures using Supabase JWT secret
- Extracts user claims from JWT tokens

### SupabaseAuthController

- REST controller for authentication endpoints
- Handles sign in, sign up, sign out, and token refresh requests

### SecurityConfiguration

- Updated to use Supabase JWT decoder
- Configures security rules for Supabase authentication

## User Data Synchronization

When a user signs in through Supabase:

1. The application retrieves user information from Supabase
2. If the user exists in the local database, their information is updated
3. If the user doesn't exist, a new user record is created with default authorities
4. User authorities and roles are managed locally in the application database

## Migration from JHipster JWT

The following changes were made:

1. **Removed**:

   - `SecurityJwtConfiguration.java`
   - `AuthenticateController.java` (old JWT controller)
   - JHipster JWT-related dependencies

2. **Added**:

   - Supabase authentication components
   - Custom JWT decoder for Supabase tokens
   - New authentication endpoints

3. **Updated**:
   - `SecurityConfiguration.java` to use Supabase JWT decoder
   - `SecurityUtils.java` to work with Supabase JWT claims
   - `AccountResource.java` to use email-based user lookup

## Security Considerations

- JWT tokens are validated using the Supabase JWT secret
- User roles and permissions are managed locally
- The application maintains its own user database for additional user data
- Supabase handles password security and user authentication
- All API endpoints (except auth endpoints) require valid JWT tokens

## Development Setup

1. Create a Supabase project at https://supabase.com
2. Configure authentication settings in your Supabase dashboard
3. Set the environment variables with your Supabase project details
4. Run the application with the `dev` profile

## Production Deployment

Ensure all Supabase environment variables are properly configured in your production environment before deploying.
