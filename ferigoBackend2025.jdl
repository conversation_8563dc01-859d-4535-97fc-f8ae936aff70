application {
  config {
    applicationType monolith
    authenticationType jwt
    baseName ferigoBackend2025
    buildTool maven
    cacheProvider redis
    clientFramework no
    creationTimestamp 1751481802074
    databaseType sql
    devDatabaseType postgresql
    enableHibernateCache true
    enableTranslation true
    jhipsterVersion "8.11.0"
    jwtSecretKey "MzgzNjk4NDY3ZWQyMzI1OGRkYzA0Yzc1ZjdlYTU0ZTM3NTAxOGI4YTYxZTgzNzViOGY3NmY2MzJmOTY2Y2FkNmQ4MzRjZmRhZDVlNzdiYjJkNDA2ZmVkMzdmMWIwM2NmMTE0Yjc4YmQ1YmJjZTk5NGU0YWRhYzQ2OWE1OTIyMjQ="
    languages [en, id]
    microfrontends []
    nativeLanguage en
    packageName com.ferigobooking.api
    prodDatabaseType postgresql
    reactive false
    skipClient true
    testFrameworks []
  }

  entities AncillaryProduct, Booking, City, CommonLookup, Country, PromoCode, PromoCodeTracking, FerryPort, FerryPortLookup, UserPassenger, BasePrice, News, NewsCategory, Operator, OperatorRoute, Order, OrderItem, Organiser, TravelPassenger, Payment, PortSector, PricingComponent, Route, RouteLookup, TripPlan, TripSchedule, PriceAdjustmentRule, Notification, NotificationTemplate, LoyaltyProgram, LoyaltyRedemption, LoyaltyReward, LoyaltyRule, LoyaltyTransaction, UserLoyaltyAccount, LoyaltyTier
}

@noFluentMethod
entity AncillaryProduct {
  name String required
  description String
  descriptionEn String
  price BigDecimal required
  productCategory String
  enabled Boolean
}
@noFluentMethod

// Entities
@noFluentMethod
entity Booking {
  code String required
  organiserFullname String required
  organiserEmail String required
  organiserPhone String required
  isRoundTrip Boolean required
  isOpenReturn Boolean required
  status BookingStatus required
  opConfirmationCode String
}
@noFluentMethod
entity City {
  name String required unique
  code String required unique minlength(6) maxlength(6)
}
@noFluentMethod
entity CommonLookup {
  key String required unique
  value String required
}
@noFluentMethod
entity Country {
  name String required unique
  code String required unique minlength(2) maxlength(2)
}
@noFluentMethod
entity PromoCode {
  code String required unique
  description String
  descriptionEn String
  isActive Boolean
}
@noFluentMethod
entity PromoCodeTracking {
  redeemedAt Instant
}
@noFluentMethod
entity FerryPort {
  name String required unique
  code String required unique minlength(3) maxlength(3)
}
@noFluentMethod
entity FerryPortLookup {
  refCode String
}
@noFluentMethod
entity UserPassenger {
  fullName String required
  gender Gender required
  dob LocalDate required
  passportNo String required
  passportExpiryDate LocalDate required
  type PassengerType required
}
@noFluentMethod
entity BasePrice {
  basePrice BigDecimal required
  passengerType PassengerType required
  directionType RouteDirection required
  nationality NationalityPrice required
  description String
}
@noFluentMethod
entity News {
  title String required
  titleEn String required
  content TextBlob required
  contentEn TextBlob required
  caption String required
  captionEn String required
  bannerUrl String
  published Boolean required
}
@noFluentMethod
entity NewsCategory {
  name String required
}
@noFluentMethod
entity Operator {
  logoUrl String required
  code String required unique minlength(3) maxlength(3)
  name String required unique
  enabled Boolean required
  instantConfirmation Boolean required
  openTicketExpiredAt LocalDate
  openTicketExpiryDurationInMonths Integer
  openTicketValidityType String required
  email String
}
@noFluentMethod
entity OperatorRoute {
  allowOpenReturn Boolean required
  allowReturn Boolean required
  enabled Boolean required
}
@noFluentMethod
entity Order (jhi_order) {
  status OrderStatus required
  totalPrice BigDecimal required
  totalDiscount BigDecimal required
  chargedAmount BigDecimal required
  totalFee BigDecimal required
  organiserName String required
  organiserPhone String required
  organiserEmail String required
  notes String
}
@noFluentMethod
entity OrderItem {
  quantity Integer required
  price BigDecimal required
  name String required
  nameEn String required
  type ProductType required
  productRefId Long required
}
@noFluentMethod
entity Organiser {
  fullName String required
  phone String required
  email String required
}
@noFluentMethod
entity TravelPassenger {
  fullName String
  gender Gender
  dob LocalDate
  passportNo String
  passportExpiryDate LocalDate
  type PassengerType
}
@noFluentMethod
entity Payment {
  status PaymentStatus required
  amountPaid BigDecimal required
  paymentMethod PaymentMethod required
  vaNumber String
  paidAt Instant
  cancelledAt Instant
  expiredAt Instant
  gatewayReferenceId String
}
@noFluentMethod
entity PortSector {
  code String
  name String
  nextSectorCode String
}
@noFluentMethod
entity PricingComponent {
  description String required
  descriptionEn String required
  type PricingComponentType required
  appliedRule String required
  amount BigDecimal required
}
@noFluentMethod
entity Route {
  code String required unique minlength(6) maxlength(6)
}
@noFluentMethod
entity RouteLookup {
  refCode String
}
@noFluentMethod
entity TripPlan {
  code String
  departureTime ZonedDateTime required
  nextTripCode String
  previousTripCode String
  durationInMinutes Integer required
  isOpenTrip Boolean
  openTicketExpiredAt LocalDate
}
@noFluentMethod
entity TripSchedule {
  day DayOfWeek required
  departTime Duration required
  durationInMinutes Integer
  enabled Boolean
}
@noFluentMethod
entity PriceAdjustmentRule {
  name String required
  nameEn String required
  adjustmentType PricingComponentType required
  targetType PriceAdjustmentTargetType required
  jsonRuleDefinition TextBlob required
  enabled Boolean required
}

@noFluentMethod
entity Notification {
  title String required
  body TextBlob required
  type NotificationType required
  channel NotificationChannel required
  targetUrl String
  isRead Boolean required
  sentAt Instant
  readAt Instant
}

@noFluentMethod
entity NotificationTemplate {
  code String required unique // e.g. BOOKING_CONFIRMED
  name String required
  description String
  type NotificationType required
  triggerType NotificationTriggerType required
  triggerEvent NotificationTriggerEvent required // if event based
  scheduledTime Instant // if AT_EXACT_TIME
  recurringCronExpression String // if recurring
  channel NotificationChannel required
  titleTemplate String required // e.g. "Booking {{code}} confirmed!"
  bodyTemplate TextBlob required // e.g. "Your booking #{{code}} for {{route}} is confirmed."
  targetUrlTemplate String // e.g. "/booking/{{id}}"
  enabled Boolean required
}

@noFluentMethod
entity LoyaltyRule {
  code String required unique // e.g., STANDARD_EARN_RATE, NEW_USER_BONUS
  name String required
  description String
  ruleType LoyaltyRuleType required // To categorize different types of loyalty rules
  jsonRuleDefinition TextBlob required // The actual business rule in JSON format
  enabled Boolean required
}

@noFluentMethod
entity LoyaltyProgram {
  name String required
  description String
  enabled Boolean required
}

@noFluentMethod
entity LoyaltyTier {
  name String required unique // e.g. Silver, Gold
  minPoints BigDecimal required
  benefits String
}

@noFluentMethod
entity UserLoyaltyAccount {
  totalPoints BigDecimal required
  availablePoints BigDecimal required
  lastUpdated Instant
}

@noFluentMethod
entity LoyaltyTransaction {
  type LoyaltyTransactionType required
  points BigDecimal required
  reason String
  referenceId String // bookingId, paymentId, etc.
  createdAt Instant required
  expiredAt Instant
}

@noFluentMethod
entity LoyaltyReward {
  code String required unique
  name String required
  description String
  pointsRequired BigDecimal required
  rewardType RewardType required
  quantity Integer
  expiryDate Instant
  isActive Boolean required
}

@noFluentMethod
entity LoyaltyRedemption {
  redeemedAt Instant required
  status RedemptionStatus required
}



// Enums
enum LoyaltyRuleType {
  EARN_RULE,
  POINT_EXPIRATION_RULE,
  TIER_QUALIFICATION_RULE,
  REDEMPTION_RULE,
  GENERAL
}
enum LoyaltyTransactionType {
  EARNED,
  REDEEMED,
  EXPIRED,
  ADJUSTMENT
}

enum RewardType {
  COUPON,
  DISCOUNT,
  PHYSICAL_ITEM
}

enum RedemptionStatus {
  PENDING, COMPLETED, FAILED
}


enum NotificationChannel {
  IN_APP,
  EMAIL,
  PUSH
}

enum NotificationType {
  SYSTEM,
  PROMOTION,
  REMINDER
}

enum NotificationTriggerType {
  MANUAL,
  EVENT_BASED,
  AT_EXACT_TIME,
  RECURRING
}

enum NotificationTriggerEvent {
  BOOKING_CONFIRMED,
  PAYMENT_COMPLETED,
  PROMO_PUBLISHED,
  USER_REGISTERED,
  POINT_EARNED,
  POINT_EXPIRED,
  POINT_REDEEMED
}

enum IdentityProvider {
  LOCAL, GOOGLE, FACEBOOK
}
enum BookingStatus {
  PENDING, PROCESSING, CONFIRMED, REJECTED, FAILED, CANCELLED
}
enum RouteDirection {
  ONEWAY,
  TWOWAY
}
enum PriceAdjustmentTargetType {
  PER_ITEM, PER_TRANSACTION
}
enum Gender {
  MALE, FEMALE, UNDEFINED
}
enum PassengerType {
  ADULT, CHILD, INFANT
}
enum OrderStatus {
  PLACED, PROCESSING, DELIVERED, CANCELLED, RETURNED, REFUNDED
}
enum ProductType {
  FERRY_BOOKING,
  ANCILLARY_PRODUCT
}
enum PaymentStatus {
  PENDING, COMPLETED, FAILED
}
enum PaymentMethod {
  VA_BCA,
  VA_BRI,
  VA_BNI,
  VA_MANDIRI,
  VA_PERMATA,
  CREDIT_CARD
}

enum PricingComponentType {
  DISCOUNT, FEE
}
enum DayOfWeek {
  MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY
}
enum NationalityPrice{
  INDONESIAN, OTHER
}

// Relationships
relationship OneToOne {
  Booking{order} to Order,
  Payment{order} to Order,
}
relationship ManyToMany {
  PromoCode{targetUsers} to User with builtInEntity
}
relationship ManyToOne {
  AncillaryProduct{operator} to Operator,
  Booking{operator required} to Operator,
  Booking{organiser} to Organiser,
  City{country required} to Country,
  PromoCode{rule} to PriceAdjustmentRule,
  PromoCodeTracking{order} to Order,
  PromoCodeTracking{code} to PromoCode,
  PromoCodeTracking{redeemedBy} to User with builtInEntity,
  FerryPort{city required} to City,
  FerryPortLookup{operator} to Operator,
  FerryPortLookup{ferryPort} to FerryPort,
  UserPassenger{nationality} to Country,
  UserPassenger{passportIssuanceCountry} to Country,
  UserPassenger{user} to User with builtInEntity,
  BasePrice{route required} to Route,
  BasePrice{operator required} to Operator,
  Order{customerUserRef} to User with builtInEntity,
  OrderItem{order} to Order,
  TravelPassenger{nationality} to Country,
  TravelPassenger{passportIssuanceCountry} to Country,
  TravelPassenger{masterPassenger} to UserPassenger,
  TravelPassenger{tripPlan} to TripPlan,
  TravelPassenger{fare} to BasePrice,
  PricingComponent{order} to Order,
  Route{startPort required} to FerryPort,
  Route{endPort required} to FerryPort,
  Route{sector} to PortSector,
  RouteLookup{operator} to Operator,
  RouteLookup{route} to Route,
  TripPlan{route} to Route,
  TripPlan{booking} to Booking,
  News{category} to NewsCategory,
  OperatorRoute{operator} to Operator,
  OperatorRoute{route} to Route,
  TripSchedule{operator required} to Operator,
  TripSchedule{route required} to Route,
  NotificationTemplate{createdBy} to User with builtInEntity,
  Notification{recipient} to User with builtInEntity,
  Notification{template} to NotificationTemplate,
  LoyaltyTransaction{user} to User with builtInEntity,
  LoyaltyTransaction{program} to LoyaltyProgram,
  UserLoyaltyAccount{user} to User with builtInEntity,
  LoyaltyRedemption{user} to User with builtInEntity,
  LoyaltyRedemption{reward} to LoyaltyReward,
  LoyaltyProgram{earnRule} to LoyaltyRule,
  UserLoyaltyAccount{tier} to LoyaltyTier
}

// --- UPDATED Generation Options ---
// This section tells JHipster to skip generating DTOs, pagination, services, and tests.
// The 'testFrameworks []' option in the application config block also helps skip tests.
dto * with mapstruct
paginate * with pagination
service * with no
search * with no
