{"generator-jhipster": {"applicationType": "monolith", "authenticationType": "jwt", "baseName": "ferigoBackend2025", "buildTool": "maven", "cacheProvider": "redis", "clientFramework": "no", "creationTimestamp": 1751481802074, "databaseType": "sql", "devDatabaseType": "postgresql", "enableHibernateCache": true, "enableTranslation": true, "entities": ["AncillaryProduct", "Booking", "City", "CommonLookup", "Country", "PromoCode", "PromoCodeTracking", "FerryPort", "FerryPortLookup", "User<PERSON>assenger", "BasePrice", "News", "NewsCategory", "Operator", "OperatorRoute", "Order", "OrderItem", "Organiser", "TravelPassenger", "Payment", "PortSector", "PricingComponent", "Route", "RouteLookup", "TripPlan", "TripSchedule", "PriceAdjustmentRule", "Notification", "NotificationTemplate", "LoyaltyProgram", "LoyaltyRedemption", "LoyaltyReward", "LoyaltyRule", "LoyaltyTransaction", "UserLoyaltyAccount", "LoyaltyTier"], "jhipsterVersion": "8.11.0", "jwtSecretKey": "MzgzNjk4NDY3ZWQyMzI1OGRkYzA0Yzc1ZjdlYTU0ZTM3NTAxOGI4YTYxZTgzNzViOGY3NmY2MzJmOTY2Y2FkNmQ4MzRjZmRhZDVlNzdiYjJkNDA2ZmVkMzdmMWIwM2NmMTE0Yjc4YmQ1YmJjZTk5NGU0YWRhYzQ2OWE1OTIyMjQ=", "languages": ["en", "id"], "lastLiquibaseTimestamp": *************, "microfrontends": [], "nativeLanguage": "en", "packageName": "com.ferigobooking.api", "prodDatabaseType": "postgresql", "reactive": false, "skipClient": true, "skipFakeData": true, "testFrameworks": []}}