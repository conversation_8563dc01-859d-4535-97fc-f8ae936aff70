#!/bin/bash

echo "========================================"
echo "Running Ferigo Backend Unit Tests"
echo "========================================"

echo ""
echo "Cleaning previous build artifacts..."
mvn clean

echo ""
echo "Compiling project..."
mvn compile test-compile

echo ""
echo "Running all unit tests..."
mvn test

echo ""
echo "Generating test report..."
mvn surefire-report:report

echo ""
echo "========================================"
echo "Test execution completed!"
echo "========================================"
echo ""
echo "Test reports can be found in:"
echo "- target/surefire-reports/"
echo "- target/site/surefire-report.html"
echo ""
