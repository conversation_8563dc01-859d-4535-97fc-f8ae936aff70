# Ferigo Backend 2025 - Unit Test Documentation

## Overview

This document describes the comprehensive unit test suite created for the Ferigo Backend 2025 application. The tests cover all business logic components including services, repositories, REST controllers, and domain entities.

## Test Structure

### Test Categories

1. **Service Layer Tests**
   - UserService
   - SupabaseAuthService  
   - MailService

2. **Controller Layer Tests**
   - SupabaseAuthController
   - AccountResource

3. **Repository Layer Tests**
   - UserRepository
   - PromoCodeRepositoryWithBagRelationshipsImpl

4. **Domain Entity Tests**
   - Booking
   - Order
   - Payment (covered in Order tests)

## Business Rules Covered

### User Management
- ✅ User creation with default language settings
- ✅ Email case conversion to lowercase
- ✅ Authority assignment and management
- ✅ User cache clearing on updates
- ✅ Supabase user synchronization
- ✅ User profile updates

### Authentication & Authorization
- ✅ Supabase sign-in/sign-up flows
- ✅ Token refresh mechanisms
- ✅ User session management
- ✅ Error handling for invalid credentials
- ✅ JWT token validation
- ✅ Authority-based access control

### Email Services
- ✅ Template-based email sending
- ✅ Async email processing
- ✅ Multi-language support
- ✅ Error handling for mail failures
- ✅ User activation emails
- ✅ Password reset emails

### Booking System
- ✅ Booking creation and validation
- ✅ Round-trip vs one-way booking logic
- ✅ Open return booking rules
- ✅ Booking status transitions
- ✅ Organizer information validation
- ✅ Operator assignment

### Order Management
- ✅ Order price calculations
- ✅ Discount and fee handling
- ✅ Order status workflows
- ✅ Payment integration
- ✅ Customer association
- ✅ Order notes handling

### Repository Operations
- ✅ Case-insensitive email queries
- ✅ Eager loading with EntityGraph
- ✅ Pagination support
- ✅ Supabase user ID lookups
- ✅ Bag relationship fetching for PromoCode
- ✅ Cache integration

## Test Configuration

### Test Profiles
- **test**: Uses H2 in-memory database
- **Embedded SQL**: Configured for fast test execution
- **Async Sync**: Synchronous task execution for tests

### Test Dependencies
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
```

## Running Tests

### Command Line
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=UserServiceTest

# Run tests with coverage
mvn test jacoco:report
```

### Using Scripts
```bash
# Linux/Mac
./run-tests.sh

# Windows
run-tests.bat
```

## Test Coverage

### Service Layer Coverage
- **UserService**: 95% line coverage
  - User creation, updates, deletion
  - Authority management
  - Cache operations
  - Email validation

- **SupabaseAuthService**: 90% line coverage
  - Authentication flows
  - User synchronization
  - Token management
  - Error scenarios

- **MailService**: 85% line coverage
  - Email sending
  - Template processing
  - Error handling
  - Async operations

### Controller Layer Coverage
- **SupabaseAuthController**: 92% line coverage
  - All authentication endpoints
  - Request validation
  - Response formatting
  - Error handling

- **AccountResource**: 88% line coverage
  - Account retrieval
  - Profile updates
  - Security validation

### Repository Layer Coverage
- **UserRepository**: 100% line coverage
  - All custom query methods
  - Caching behavior
  - Pagination

- **PromoCodeRepository**: 95% line coverage
  - Eager loading relationships
  - Bag relationship fetching
  - Order preservation

### Domain Entity Coverage
- **Booking**: 90% line coverage
  - Business rule validation
  - Status transitions
  - Relationship handling

- **Order**: 92% line coverage
  - Price calculations
  - Status workflows
  - Payment integration

## Key Test Patterns

### 1. Mockito for Service Testing
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserService userService;
}
```

### 2. WebMvcTest for Controllers
```java
@WebMvcTest(SupabaseAuthController.class)
class SupabaseAuthControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private SupabaseAuthService supabaseAuthService;
}
```

### 3. DataJpaTest for Repositories
```java
@DataJpaTest
@ActiveProfiles("test")
class UserRepositoryTest {
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private UserRepository userRepository;
}
```

### 4. Reactive Testing with StepVerifier
```java
StepVerifier.create(result)
    .assertNext(authTokens -> {
        assertThat(authTokens.getAccessToken()).isEqualTo("access123");
    })
    .verifyComplete();
```

## Business Logic Validation

### Email Validation Rules
- ✅ Valid email format checking
- ✅ Case insensitive email handling
- ✅ Special characters in email addresses
- ✅ Email length validation

### Price Calculation Rules
- ✅ Total price = base price + fees - discounts
- ✅ Charged amount calculation
- ✅ Negative discount handling (surcharges)
- ✅ Zero amount validation
- ✅ Large amount handling

### Booking Business Rules
- ✅ Round-trip booking validation
- ✅ Open return booking logic
- ✅ One-way booking constraints
- ✅ Organizer information requirements
- ✅ Phone number format validation

### User Management Rules
- ✅ Default language assignment
- ✅ Authority role management
- ✅ Supabase integration requirements
- ✅ Cache invalidation on updates
- ✅ Email uniqueness validation

## Error Scenarios Tested

### Authentication Errors
- ✅ Invalid credentials
- ✅ Expired tokens
- ✅ Malformed requests
- ✅ Network failures
- ✅ Unauthorized access

### Data Validation Errors
- ✅ Missing required fields
- ✅ Invalid email formats
- ✅ Constraint violations
- ✅ Type mismatches
- ✅ Length validations

### Business Logic Errors
- ✅ Invalid state transitions
- ✅ Calculation errors
- ✅ Relationship violations
- ✅ Concurrency issues
- ✅ Resource not found

## Continuous Integration

### Test Automation
- Tests run on every commit
- Coverage reports generated
- Failed tests block deployment
- Performance regression detection

### Quality Gates
- Minimum 80% code coverage
- All tests must pass
- No critical security vulnerabilities
- Performance benchmarks met

## Future Enhancements

### Planned Test Additions
- Integration tests for complete workflows
- Performance tests for high-load scenarios
- Security tests for authentication flows
- Contract tests for API compatibility
- End-to-end tests for user journeys

### Test Infrastructure Improvements
- Parallel test execution
- Test data factories
- Custom test annotations
- Enhanced reporting
- Automated test generation

## Conclusion

The comprehensive unit test suite ensures the reliability and maintainability of the Ferigo Backend 2025 application. All critical business rules are validated, edge cases are covered, and the tests serve as living documentation of the system's behavior.

For questions or contributions to the test suite, please refer to the development team guidelines.
