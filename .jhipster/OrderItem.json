{"annotations": {"changelogDate": "20250705161708"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "quantity", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"fieldName": "price", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "nameEn", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "ProductType", "fieldValidateRules": ["required"], "fieldValues": "FERRY_BOOKING,ANCILLARY_PRODUCT"}, {"fieldName": "productRefId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}], "name": "OrderItem", "pagination": "pagination", "relationships": [{"otherEntityName": "order", "relationshipName": "order", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}