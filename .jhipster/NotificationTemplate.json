{"annotations": {"changelogDate": "20250705161721"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "type", "fieldType": "NotificationType", "fieldValidateRules": ["required"], "fieldValues": "SYSTEM,PROMOTION,REMINDER"}, {"fieldName": "triggerType", "fieldType": "NotificationTriggerType", "fieldValidateRules": ["required"], "fieldValues": "MANUAL,EVENT_BASED,AT_EXACT_TIME,RECURRING"}, {"fieldName": "triggerEvent", "fieldType": "NotificationTriggerEvent", "fieldValidateRules": ["required"], "fieldValues": "BOOKING_CONFIRMED,PAYMENT_COMPLETED,PROMO_PUBLISHED,USER_REGISTERED,POINT_EARNED,POINT_EXPIRED,POINT_REDEEMED"}, {"fieldName": "scheduledTime", "fieldType": "Instant"}, {"fieldName": "recurringCronExpression", "fieldType": "String"}, {"fieldName": "channel", "fieldType": "NotificationChannel", "fieldValidateRules": ["required"], "fieldValues": "IN_APP,EMAIL,PUSH"}, {"fieldName": "titleTemplate", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "bodyTemplate", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"fieldName": "targetUrlTemplate", "fieldType": "String"}, {"fieldName": "enabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "name": "NotificationTemplate", "pagination": "pagination", "relationships": [{"otherEntityName": "userProfile", "relationshipName": "created<PERSON>y", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}