{"annotations": {"changelogDate": "20250705161714"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique", "minlength", "maxlength"], "fieldValidateRulesMaxlength": "6", "fieldValidateRulesMinlength": "6"}], "name": "Route", "pagination": "pagination", "relationships": [{"otherEntityName": "ferryPort", "relationshipName": "startPort", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}, {"otherEntityName": "ferryPort", "relationshipName": "endPort", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}, {"otherEntityName": "portSector", "relationshipName": "sector", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}