{"annotations": {"changelogDate": "20250705161654"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique", "minlength", "maxlength"], "fieldValidateRulesMaxlength": "6", "fieldValidateRulesMinlength": "6"}], "name": "City", "pagination": "pagination", "relationships": [{"otherEntityName": "country", "relationshipName": "country", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}], "searchEngine": "no", "service": "no"}