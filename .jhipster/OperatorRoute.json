{"annotations": {"changelogDate": "20250705161706"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "allowOpenReturn", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "allowReturn", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "enabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "name": "OperatorRoute", "pagination": "pagination", "relationships": [{"otherEntityName": "operator", "relationshipName": "operator", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "route", "relationshipName": "route", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}