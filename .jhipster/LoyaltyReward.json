{"annotations": {"changelogDate": "20250705161724"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "pointsRequired", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "rewardType", "fieldType": "RewardType", "fieldValidateRules": ["required"], "fieldValues": "COUPON,DISCOUNT,PHYSICAL_ITEM"}, {"fieldName": "quantity", "fieldType": "Integer"}, {"fieldName": "expiryDate", "fieldType": "Instant"}, {"fieldName": "isActive", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "name": "LoyaltyReward", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "no"}