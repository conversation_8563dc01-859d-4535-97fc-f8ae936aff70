{"annotations": {"changelogDate": "20250705161656"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique", "minlength", "maxlength"], "fieldValidateRulesMaxlength": "2", "fieldValidateRulesMinlength": "2"}], "name": "Country", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "no"}