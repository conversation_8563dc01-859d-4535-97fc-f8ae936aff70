{"annotations": {"changelogDate": "20250705161713"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "description", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "descriptionEn", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "PricingComponentType", "fieldValidateRules": ["required"], "fieldValues": "DISCOUNT,FEE"}, {"fieldName": "appliedRule", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "amount", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}], "name": "PricingComponent", "pagination": "pagination", "relationships": [{"otherEntityName": "order", "relationshipName": "order", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}