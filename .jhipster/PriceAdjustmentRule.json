{"annotations": {"changelogDate": "20250705161719"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "nameEn", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "adjustmentType", "fieldType": "PricingComponentType", "fieldValidateRules": ["required"], "fieldValues": "DISCOUNT,FEE"}, {"fieldName": "targetType", "fieldType": "PriceAdjustmentTargetType", "fieldValidateRules": ["required"], "fieldValues": "PER_ITEM,PER_TRANSACTION"}, {"fieldName": "jsonRuleDefinition", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"fieldName": "enabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "name": "PriceAdjustmentRule", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "no"}