{"annotations": {"changelogDate": "20250705161728"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "minPoints", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "benefits", "fieldType": "String"}], "name": "LoyaltyTier", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "no"}