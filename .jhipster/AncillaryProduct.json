{"annotations": {"changelogDate": "20250705161652"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "descriptionEn", "fieldType": "String"}, {"fieldName": "price", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "productCategory", "fieldType": "String"}, {"fieldName": "enabled", "fieldType": "Boolean"}], "name": "AncillaryProduct", "pagination": "pagination", "relationships": [{"otherEntityName": "operator", "relationshipName": "operator", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}