{"annotations": {"changelogDate": "20250705161657"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "descriptionEn", "fieldType": "String"}, {"fieldName": "isActive", "fieldType": "Boolean"}], "name": "PromoCode", "pagination": "pagination", "relationships": [{"otherEntityName": "priceAdjustmentRule", "relationshipName": "rule", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "userProfile", "relationshipName": "targetUsers", "relationshipSide": "left", "relationshipType": "many-to-many"}], "searchEngine": "no", "service": "no"}