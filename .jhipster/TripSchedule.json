{"annotations": {"changelogDate": "20250705161717"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "day", "fieldType": "DayOfWeek", "fieldValidateRules": ["required"], "fieldValues": "MONDAY,TUESDAY,WEDNESDAY,THURSDAY,FRIDAY,SATURDAY,SUNDAY"}, {"fieldName": "departTime", "fieldType": "Duration", "fieldValidateRules": ["required"]}, {"fieldName": "durationInMinutes", "fieldType": "Integer"}, {"fieldName": "enabled", "fieldType": "Boolean"}], "name": "TripSchedule", "pagination": "pagination", "relationships": [{"otherEntityName": "operator", "relationshipName": "operator", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}, {"otherEntityName": "route", "relationshipName": "route", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}], "searchEngine": "no", "service": "no"}