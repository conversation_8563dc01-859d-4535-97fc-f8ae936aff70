{"annotations": {"changelogDate": "20250705161653"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "organiser<PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "organiserEmail", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "organiserPhone", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "isRoundTrip", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "isOpenReturn", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "status", "fieldType": "BookingStatus", "fieldValidateRules": ["required"], "fieldValues": "PENDING,PROCESSING,CONFIRMED,REJECTED,FAILED,CANCELLED"}, {"fieldName": "opConfirmationCode", "fieldType": "String"}], "name": "Booking", "pagination": "pagination", "relationships": [{"otherEntityName": "order", "relationshipName": "order", "relationshipSide": "left", "relationshipType": "one-to-one"}, {"otherEntityName": "operator", "relationshipName": "operator", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}, {"otherEntityName": "organiser", "relationshipName": "organiser", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}