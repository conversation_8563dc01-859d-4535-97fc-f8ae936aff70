{"annotations": {"changelogDate": "20250705161716"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "code", "fieldType": "String"}, {"fieldName": "departureTime", "fieldType": "ZonedDateTime", "fieldValidateRules": ["required"]}, {"fieldName": "nextTripCode", "fieldType": "String"}, {"fieldName": "previousTripCode", "fieldType": "String"}, {"fieldName": "durationInMinutes", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"fieldName": "isOpenTrip", "fieldType": "Boolean"}, {"fieldName": "openTicketExpiredAt", "fieldType": "LocalDate"}], "name": "TripPlan", "pagination": "pagination", "relationships": [{"otherEntityName": "route", "relationshipName": "route", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "booking", "relationshipName": "booking", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}