{"annotations": {"changelogDate": "20250705161720"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "title", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "body", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "NotificationType", "fieldValidateRules": ["required"], "fieldValues": "SYSTEM,PROMOTION,REMINDER"}, {"fieldName": "channel", "fieldType": "NotificationChannel", "fieldValidateRules": ["required"], "fieldValues": "IN_APP,EMAIL,PUSH"}, {"fieldName": "targetUrl", "fieldType": "String"}, {"fieldName": "isRead", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "sentAt", "fieldType": "Instant"}, {"fieldName": "readAt", "fieldType": "Instant"}], "name": "Notification", "pagination": "pagination", "relationships": [{"otherEntityName": "userProfile", "relationshipName": "recipient", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "notificationTemplate", "relationshipName": "template", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}