{"annotations": {"changelogDate": "20250705161659"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique", "minlength", "maxlength"], "fieldValidateRulesMaxlength": "3", "fieldValidateRulesMinlength": "3"}], "name": "FerryPort", "pagination": "pagination", "relationships": [{"otherEntityName": "city", "relationshipName": "city", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}], "searchEngine": "no", "service": "no"}