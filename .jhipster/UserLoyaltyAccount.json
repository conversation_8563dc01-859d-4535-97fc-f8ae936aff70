{"annotations": {"changelogDate": "**************"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "totalPoints", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "availablePoints", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "lastUpdated", "fieldType": "Instant"}], "name": "UserLoyaltyAccount", "pagination": "pagination", "relationships": [{"otherEntityName": "userProfile", "relationshipName": "user", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "loyaltyTier", "relationshipName": "tier", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}