{"annotations": {"changelogDate": "20250705161702"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "basePrice", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "passengerType", "fieldType": "PassengerType", "fieldValidateRules": ["required"], "fieldValues": "ADULT,CHILD,INFANT"}, {"fieldName": "directionType", "fieldType": "RouteDirection", "fieldValidateRules": ["required"], "fieldValues": "ONEWAY,TWOWAY"}, {"fieldName": "nationality", "fieldType": "NationalityPrice", "fieldValidateRules": ["required"], "fieldValues": "INDONESIAN,OTHER"}, {"fieldName": "description", "fieldType": "String"}], "name": "BasePrice", "pagination": "pagination", "relationships": [{"otherEntityName": "route", "relationshipName": "route", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}, {"otherEntityName": "operator", "relationshipName": "operator", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required"}], "searchEngine": "no", "service": "no"}