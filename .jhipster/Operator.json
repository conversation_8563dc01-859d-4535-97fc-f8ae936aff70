{"annotations": {"changelogDate": "20250705161705"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "logoUrl", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique", "minlength", "maxlength"], "fieldValidateRulesMaxlength": "3", "fieldValidateRulesMinlength": "3"}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "enabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "instantConfirmation", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "openTicketExpiredAt", "fieldType": "LocalDate"}, {"fieldName": "openTicketExpiryDurationInMonths", "fieldType": "Integer"}, {"fieldName": "openTicketValidityType", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "email", "fieldType": "String"}], "name": "Operator", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "no"}