{"annotations": {"changelogDate": "20250705161723"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "redeemedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "status", "fieldType": "RedemptionStatus", "fieldValidateRules": ["required"], "fieldValues": "PENDING,COMPLETED,FAILED"}], "name": "LoyaltyRedemption", "pagination": "pagination", "relationships": [{"otherEntityName": "userProfile", "relationshipName": "user", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "loyaltyReward", "relationshipName": "reward", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}