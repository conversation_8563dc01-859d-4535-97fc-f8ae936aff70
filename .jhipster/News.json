{"annotations": {"changelogDate": "20250705161703"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "title", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "titleEn", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "content", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"fieldName": "contentEn", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"fieldName": "caption", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "captionEn", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "bannerUrl", "fieldType": "String"}, {"fieldName": "published", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "name": "News", "pagination": "pagination", "relationships": [{"otherEntityName": "newsCategory", "relationshipName": "category", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}