{"annotations": {"changelogDate": "20250705161701"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "fullName", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "gender", "fieldType": "Gender", "fieldValidateRules": ["required"], "fieldValues": "MALE,FEMALE,UNDEFINED"}, {"fieldName": "dob", "fieldType": "LocalDate", "fieldValidateRules": ["required"]}, {"fieldName": "passportNo", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "passportExpiryDate", "fieldType": "LocalDate", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "PassengerType", "fieldValidateRules": ["required"], "fieldValues": "ADULT,CHILD,INFANT"}], "name": "User<PERSON>assenger", "pagination": "pagination", "relationships": [{"otherEntityName": "country", "relationshipName": "nationality", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "country", "relationshipName": "passportIssuanceCountry", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "userProfile", "relationshipName": "user", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}