{"annotations": {"changelogDate": "20250705161707"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "entityTableName": "jhi_order", "fields": [{"fieldName": "status", "fieldType": "OrderStatus", "fieldValidateRules": ["required"], "fieldValues": "PLACED,PROCESSING,DELIVERED,CANCELLED,RETURNED,REFUNDED"}, {"fieldName": "totalPrice", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "totalDiscount", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "chargedAmount", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "totalFee", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "organiserName", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "organiserPhone", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "organiserEmail", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "notes", "fieldType": "String"}], "name": "Order", "pagination": "pagination", "relationships": [{"otherEntityName": "userProfile", "relationshipName": "customerUserRef", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}