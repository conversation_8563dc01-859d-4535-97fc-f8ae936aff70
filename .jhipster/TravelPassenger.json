{"annotations": {"changelogDate": "20250705161710"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "fullName", "fieldType": "String"}, {"fieldName": "gender", "fieldType": "Gender", "fieldValues": "MALE,FEMALE,UNDEFINED"}, {"fieldName": "dob", "fieldType": "LocalDate"}, {"fieldName": "passportNo", "fieldType": "String"}, {"fieldName": "passportExpiryDate", "fieldType": "LocalDate"}, {"fieldName": "type", "fieldType": "PassengerType", "fieldValues": "ADULT,CHILD,INFANT"}], "name": "TravelPassenger", "pagination": "pagination", "relationships": [{"otherEntityName": "country", "relationshipName": "nationality", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "country", "relationshipName": "passportIssuanceCountry", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "user<PERSON>assenger", "relationshipName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "tripPlan", "relationshipName": "tripPlan", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "basePrice", "relationshipName": "fare", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}