{"annotations": {"changelogDate": "20250705161725"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "ruleType", "fieldType": "LoyaltyRuleType", "fieldValidateRules": ["required"], "fieldValues": "EARN_RULE,POINT_EXPIRATION_RULE,TIER_QUALIFICATION_RULE,REDEMPTION_RULE,GENERAL"}, {"fieldName": "jsonRuleDefinition", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"fieldName": "enabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "name": "LoyaltyRule", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "no"}