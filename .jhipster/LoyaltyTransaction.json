{"annotations": {"changelogDate": "20250705161726"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "type", "fieldType": "LoyaltyTransactionType", "fieldValidateRules": ["required"], "fieldValues": "EARNED,REDEEMED,EXPIRED,ADJUSTMENT"}, {"fieldName": "points", "fieldType": "BigDecimal", "fieldValidateRules": ["required"]}, {"fieldName": "reason", "fieldType": "String"}, {"fieldName": "referenceId", "fieldType": "String"}, {"fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "expiredAt", "fieldType": "Instant"}], "name": "LoyaltyTransaction", "pagination": "pagination", "relationships": [{"otherEntityName": "userProfile", "relationshipName": "user", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "loyaltyProgram", "relationshipName": "program", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}