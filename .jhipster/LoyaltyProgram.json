{"annotations": {"changelogDate": "20250705161722"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "enabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "name": "LoyaltyProgram", "pagination": "pagination", "relationships": [{"otherEntityName": "loyaltyRule", "relationshipName": "earnRule", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}