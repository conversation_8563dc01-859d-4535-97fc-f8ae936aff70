{"annotations": {"changelogDate": "20250705161658"}, "applications": ["ferigoBackend2025"], "dto": "mapstruct", "fields": [{"fieldName": "redeemedAt", "fieldType": "Instant"}], "name": "PromoCodeTracking", "pagination": "pagination", "relationships": [{"otherEntityName": "order", "relationshipName": "order", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "promoCode", "relationshipName": "code", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "userProfile", "relationshipName": "redeemed<PERSON><PERSON>", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "no"}