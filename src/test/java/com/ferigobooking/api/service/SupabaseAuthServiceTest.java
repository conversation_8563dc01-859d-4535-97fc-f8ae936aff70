package com.ferigobooking.api.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ferigobooking.api.config.SupabaseProperties;
import com.ferigobooking.api.domain.Authority;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.repository.AuthorityRepository;
import com.ferigobooking.api.repository.UserRepository;
import com.ferigobooking.api.security.AuthoritiesConstants;
import com.ferigobooking.api.service.dto.AuthTokensDTO;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class SupabaseAuthServiceTest {

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.RequestBodySpec requestBodySpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @Mock
    private UserRepository userRepository;

    @Mock
    private AuthorityRepository authorityRepository;

    @Mock
    private SupabaseProperties supabaseProperties;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private SupabaseAuthService supabaseAuthService;

    private User testUser;
    private Authority userAuthority;

    @BeforeEach
    void setUp() {
        userAuthority = new Authority();
        userAuthority.setName(AuthoritiesConstants.USER);

        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setSupabaseUserId("supabase-123");
        testUser.setAuthorities(Set.of(userAuthority));
    }

    @Test
    void signInWithEmailPassword_ShouldReturnAuthTokens() {
        // Given
        String email = "<EMAIL>";
        String password = "password123";
        String responseBody = "{\"access_token\":\"access123\",\"refresh_token\":\"refresh123\"}";

        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/auth/v1/token?grant_type=password")).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(responseBody));

        // When
        Mono<AuthTokensDTO> result = supabaseAuthService.signInWithEmailPassword(email, password);

        // Then
        StepVerifier.create(result)
            .assertNext(authTokens -> {
                assertThat(authTokens.getAccessToken()).isEqualTo("access123");
                assertThat(authTokens.getRefreshToken()).isEqualTo("refresh123");
            })
            .verifyComplete();

        verify(requestBodySpec).bodyValue(contains(email));
        verify(requestBodySpec).bodyValue(contains(password));
    }

    @Test
    void signUpWithEmailPassword_ShouldReturnAuthTokens() {
        // Given
        String email = "<EMAIL>";
        String password = "password123";
        String firstName = "New";
        String lastName = "User";
        String responseBody = "{\"access_token\":\"access123\",\"refresh_token\":\"refresh123\"}";

        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/auth/v1/signup")).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(responseBody));

        // When
        Mono<AuthTokensDTO> result = supabaseAuthService.signUpWithEmailPassword(email, password, firstName, lastName);

        // Then
        StepVerifier.create(result)
            .assertNext(authTokens -> {
                assertThat(authTokens.getAccessToken()).isEqualTo("access123");
                assertThat(authTokens.getRefreshToken()).isEqualTo("refresh123");
            })
            .verifyComplete();

        verify(requestBodySpec).bodyValue(contains(email));
        verify(requestBodySpec).bodyValue(contains(firstName));
        verify(requestBodySpec).bodyValue(contains(lastName));
    }

    @Test
    void signUpWithEmailPassword_ShouldHandleNullNames() {
        // Given
        String email = "<EMAIL>";
        String password = "password123";
        String responseBody = "{\"access_token\":\"access123\",\"refresh_token\":\"refresh123\"}";

        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/auth/v1/signup")).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(responseBody));

        // When
        Mono<AuthTokensDTO> result = supabaseAuthService.signUpWithEmailPassword(email, password, null, null);

        // Then
        StepVerifier.create(result)
            .assertNext(authTokens -> {
                assertThat(authTokens.getAccessToken()).isEqualTo("access123");
                assertThat(authTokens.getRefreshToken()).isEqualTo("refresh123");
            })
            .verifyComplete();

        verify(requestBodySpec).bodyValue(contains("\"first_name\":\"\""));
        verify(requestBodySpec).bodyValue(contains("\"last_name\":\"\""));
    }

    @Test
    void refreshToken_ShouldReturnNewAuthTokens() {
        // Given
        String refreshToken = "refresh123";
        String responseBody = "{\"access_token\":\"new_access123\",\"refresh_token\":\"new_refresh123\"}";

        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/auth/v1/token?grant_type=refresh_token")).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(responseBody));

        // When
        Mono<AuthTokensDTO> result = supabaseAuthService.refreshToken(refreshToken);

        // Then
        StepVerifier.create(result)
            .assertNext(authTokens -> {
                assertThat(authTokens.getAccessToken()).isEqualTo("new_access123");
                assertThat(authTokens.getRefreshToken()).isEqualTo("new_refresh123");
            })
            .verifyComplete();

        verify(requestBodySpec).bodyValue(contains(refreshToken));
    }

    @Test
    void signOut_ShouldCallLogoutEndpoint() {
        // Given
        String accessToken = "access123";

        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/auth/v1/logout")).thenReturn(requestBodySpec);
        when(requestBodySpec.header(eq("Authorization"), eq("Bearer " + accessToken))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(Void.class)).thenReturn(Mono.empty());

        // When
        Mono<Void> result = supabaseAuthService.signOut(accessToken);

        // Then
        StepVerifier.create(result)
            .verifyComplete();

        verify(requestBodySpec).header("Authorization", "Bearer " + accessToken);
    }

    @Test
    void getUserFromSupabase_ShouldReturnExistingUser() {
        // Given
        String accessToken = "access123";
        String userResponse = "{\"id\":\"supabase-123\",\"email\":\"<EMAIL>\",\"user_metadata\":{\"first_name\":\"Test\",\"last_name\":\"User\"}}";

        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/auth/v1/user")).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.header(eq("Authorization"), eq("Bearer " + accessToken))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(userResponse));
        when(userRepository.findOneBySupabaseUserId("supabase-123")).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // When
        Mono<User> result = supabaseAuthService.getUserFromSupabase(accessToken);

        // Then
        StepVerifier.create(result)
            .assertNext(user -> {
                assertThat(user.getEmail()).isEqualTo("<EMAIL>");
                assertThat(user.getSupabaseUserId()).isEqualTo("supabase-123");
            })
            .verifyComplete();

        verify(userRepository).findOneBySupabaseUserId("supabase-123");
        verify(userRepository).save(testUser);
    }

    @Test
    void getUserFromSupabase_ShouldCreateNewUser() {
        // Given
        String accessToken = "access123";
        String userResponse = "{\"id\":\"supabase-456\",\"email\":\"<EMAIL>\",\"user_metadata\":{\"first_name\":\"New\",\"last_name\":\"User\"}}";

        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/auth/v1/user")).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.header(eq("Authorization"), eq("Bearer " + accessToken))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(userResponse));
        when(userRepository.findOneBySupabaseUserId("supabase-456")).thenReturn(Optional.empty());
        when(authorityRepository.findById(AuthoritiesConstants.USER)).thenReturn(Optional.of(userAuthority));
        when(userRepository.save(any(User.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        Mono<User> result = supabaseAuthService.getUserFromSupabase(accessToken);

        // Then
        StepVerifier.create(result)
            .assertNext(user -> {
                assertThat(user.getEmail()).isEqualTo("<EMAIL>");
                assertThat(user.getSupabaseUserId()).isEqualTo("supabase-456");
                assertThat(user.getFirstName()).isEqualTo("New");
                assertThat(user.getLastName()).isEqualTo("User");
                assertThat(user.getLangKey()).isEqualTo("en");
                assertThat(user.getAuthorities()).contains(userAuthority);
            })
            .verifyComplete();

        verify(userRepository).findOneBySupabaseUserId("supabase-456");
        verify(authorityRepository).findById(AuthoritiesConstants.USER);
        verify(userRepository).save(any(User.class));
    }

    @Test
    void parseSupabaseUser_ShouldHandleCompleteUserData() {
        // Given
        String userJson = """
            {
                "id": "supabase-123",
                "email": "<EMAIL>",
                "phone": "+**********",
                "email_confirmed_at": "2023-01-01T00:00:00Z",
                "phone_confirmed_at": "2023-01-01T00:00:00Z",
                "user_metadata": {
                    "first_name": "Test",
                    "last_name": "User",
                    "avatar_url": "http://example.com/avatar.jpg"
                },
                "app_metadata": {
                    "provider": "google",
                    "providers": ["google"]
                }
            }
            """;

        // This test would require making parseSupabaseUser public or using reflection
        // For now, we test it indirectly through getUserFromSupabase
    }

    @Test
    void syncUserWithDatabase_ShouldUpdateExistingUserFields() {
        // Given - This is tested indirectly through getUserFromSupabase
        // The method updates user fields when syncing with database
        String accessToken = "access123";
        String userResponse = "{\"id\":\"supabase-123\",\"email\":\"<EMAIL>\",\"user_metadata\":{\"first_name\":\"Updated\",\"last_name\":\"Name\"}}";

        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/auth/v1/user")).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.header(eq("Authorization"), eq("Bearer " + accessToken))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(userResponse));
        when(userRepository.findOneBySupabaseUserId("supabase-123")).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // When
        Mono<User> result = supabaseAuthService.getUserFromSupabase(accessToken);

        // Then
        StepVerifier.create(result)
            .assertNext(user -> {
                assertThat(user.getEmail()).isEqualTo("<EMAIL>");
                assertThat(user.getFirstName()).isEqualTo("Updated");
                assertThat(user.getLastName()).isEqualTo("Name");
            })
            .verifyComplete();
    }

    private String contains(String substring) {
        return argThat(argument -> argument.toString().contains(substring));
    }
}
