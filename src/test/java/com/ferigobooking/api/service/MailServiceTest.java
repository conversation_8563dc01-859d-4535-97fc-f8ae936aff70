package com.ferigobooking.api.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.ferigobooking.api.domain.User;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import tech.jhipster.config.JHipsterProperties;

@ExtendWith(MockitoExtension.class)
class MailServiceTest {

    @Mock
    private JHipsterProperties jHipsterProperties;

    @Mock
    private JHipsterProperties.Mail mailProperties;

    @Mock
    private JavaMailSender javaMailSender;

    @Mock
    private MessageSource messageSource;

    @Mock
    private SpringTemplateEngine templateEngine;

    @Mock
    private MimeMessage mimeMessage;

    @InjectMocks
    private MailService mailService;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setLangKey("en");

        when(jHipsterProperties.getMail()).thenReturn(mailProperties);
        when(mailProperties.getFrom()).thenReturn("<EMAIL>");
        when(mailProperties.getBaseUrl()).thenReturn("http://localhost:8080");
    }

    @Test
    void sendEmail_ShouldSendEmailSuccessfully() throws MessagingException {
        // Given
        String to = "<EMAIL>";
        String subject = "Test Subject";
        String content = "Test Content";
        boolean isMultipart = false;
        boolean isHtml = true;

        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendEmail(to, subject, content, isMultipart, isHtml);

        // Then
        verify(javaMailSender).createMimeMessage();
        verify(javaMailSender).send(mimeMessage);
    }

    @Test
    void sendEmail_ShouldHandleMailException() throws MessagingException {
        // Given
        String to = "<EMAIL>";
        String subject = "Test Subject";
        String content = "Test Content";

        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doThrow(new MailException("Mail server error") {}).when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendEmail(to, subject, content, false, true);

        // Then
        verify(javaMailSender).createMimeMessage();
        verify(javaMailSender).send(mimeMessage);
        // Should not throw exception, just log warning
    }

    @Test
    void sendEmailFromTemplate_ShouldProcessTemplateAndSendEmail() {
        // Given
        String templateName = "mail/testEmail";
        String titleKey = "email.test.title";
        String processedContent = "<html><body>Test Email Content</body></html>";
        String subject = "Test Email Subject";

        when(templateEngine.process(eq(templateName), any(Context.class))).thenReturn(processedContent);
        when(messageSource.getMessage(eq(titleKey), eq(null), any(Locale.class))).thenReturn(subject);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendEmailFromTemplate(testUser, templateName, titleKey);

        // Then
        verify(templateEngine).process(eq(templateName), any(Context.class));
        verify(messageSource).getMessage(eq(titleKey), eq(null), any(Locale.class));
        verify(javaMailSender).createMimeMessage();
        verify(javaMailSender).send(mimeMessage);
    }

    @Test
    void sendEmailFromTemplate_ShouldSkipWhenUserEmailIsNull() {
        // Given
        testUser.setEmail(null);
        String templateName = "mail/testEmail";
        String titleKey = "email.test.title";

        // When
        mailService.sendEmailFromTemplate(testUser, templateName, titleKey);

        // Then
        verify(templateEngine, never()).process(anyString(), any(Context.class));
        verify(messageSource, never()).getMessage(anyString(), any(), any(Locale.class));
        verify(javaMailSender, never()).createMimeMessage();
    }

    @Test
    void sendEmailFromTemplate_ShouldUseUserLanguageForLocale() {
        // Given
        testUser.setLangKey("id"); // Indonesian
        String templateName = "mail/testEmail";
        String titleKey = "email.test.title";
        String processedContent = "<html><body>Test Email Content</body></html>";
        String subject = "Test Email Subject";

        when(templateEngine.process(eq(templateName), any(Context.class))).thenReturn(processedContent);
        when(messageSource.getMessage(eq(titleKey), eq(null), eq(Locale.forLanguageTag("id")))).thenReturn(subject);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendEmailFromTemplate(testUser, templateName, titleKey);

        // Then
        verify(messageSource).getMessage(eq(titleKey), eq(null), eq(Locale.forLanguageTag("id")));
    }

    @Test
    void sendEmailFromTemplate_ShouldSetContextVariables() {
        // Given
        String templateName = "mail/testEmail";
        String titleKey = "email.test.title";
        String processedContent = "<html><body>Test Email Content</body></html>";
        String subject = "Test Email Subject";

        when(templateEngine.process(eq(templateName), any(Context.class))).thenAnswer(invocation -> {
            Context context = invocation.getArgument(1);
            // Verify context contains expected variables
            assert context.getVariable("user").equals(testUser);
            assert context.getVariable("baseUrl").equals("http://localhost:8080");
            return processedContent;
        });
        when(messageSource.getMessage(eq(titleKey), eq(null), any(Locale.class))).thenReturn(subject);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendEmailFromTemplate(testUser, templateName, titleKey);

        // Then
        verify(templateEngine).process(eq(templateName), any(Context.class));
    }

    @Test
    void sendActivationEmail_ShouldSendActivationTemplate() {
        // Given
        String processedContent = "<html><body>Activation Email</body></html>";
        String subject = "Account Activation";

        when(templateEngine.process(eq("mail/activationEmail"), any(Context.class))).thenReturn(processedContent);
        when(messageSource.getMessage(eq("email.activation.title"), eq(null), any(Locale.class))).thenReturn(subject);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendActivationEmail(testUser);

        // Then
        verify(templateEngine).process(eq("mail/activationEmail"), any(Context.class));
        verify(messageSource).getMessage(eq("email.activation.title"), eq(null), any(Locale.class));
        verify(javaMailSender).send(mimeMessage);
    }

    @Test
    void sendCreationEmail_ShouldSendCreationTemplate() {
        // Given
        String processedContent = "<html><body>Creation Email</body></html>";
        String subject = "Account Created";

        when(templateEngine.process(eq("mail/creationEmail"), any(Context.class))).thenReturn(processedContent);
        when(messageSource.getMessage(eq("email.activation.title"), eq(null), any(Locale.class))).thenReturn(subject);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendCreationEmail(testUser);

        // Then
        verify(templateEngine).process(eq("mail/creationEmail"), any(Context.class));
        verify(messageSource).getMessage(eq("email.activation.title"), eq(null), any(Locale.class));
        verify(javaMailSender).send(mimeMessage);
    }

    @Test
    void sendPasswordResetMail_ShouldSendPasswordResetTemplate() {
        // Given
        String processedContent = "<html><body>Password Reset Email</body></html>";
        String subject = "Password Reset";

        when(templateEngine.process(eq("mail/passwordResetEmail"), any(Context.class))).thenReturn(processedContent);
        when(messageSource.getMessage(eq("email.reset.title"), eq(null), any(Locale.class))).thenReturn(subject);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendPasswordResetMail(testUser);

        // Then
        verify(templateEngine).process(eq("mail/passwordResetEmail"), any(Context.class));
        verify(messageSource).getMessage(eq("email.reset.title"), eq(null), any(Locale.class));
        verify(javaMailSender).send(mimeMessage);
    }

    @Test
    void sendEmail_ShouldHandleMessagingException() throws MessagingException {
        // Given
        String to = "<EMAIL>";
        String subject = "Test Subject";
        String content = "Test Content";

        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doThrow(new MessagingException("Message creation failed")).when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendEmail(to, subject, content, false, true);

        // Then
        verify(javaMailSender).createMimeMessage();
        verify(javaMailSender).send(mimeMessage);
        // Should not throw exception, just log warning
    }

    @Test
    void sendEmailFromTemplate_ShouldHandleDefaultLanguageWhenLangKeyIsNull() {
        // Given
        testUser.setLangKey(null);
        String templateName = "mail/testEmail";
        String titleKey = "email.test.title";
        String processedContent = "<html><body>Test Email Content</body></html>";
        String subject = "Test Email Subject";

        when(templateEngine.process(eq(templateName), any(Context.class))).thenReturn(processedContent);
        when(messageSource.getMessage(eq(titleKey), eq(null), any(Locale.class))).thenReturn(subject);
        when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(javaMailSender).send(any(MimeMessage.class));

        // When
        mailService.sendEmailFromTemplate(testUser, templateName, titleKey);

        // Then
        verify(templateEngine).process(eq(templateName), any(Context.class));
        verify(messageSource).getMessage(eq(titleKey), eq(null), any(Locale.class));
        verify(javaMailSender).send(mimeMessage);
    }
}
