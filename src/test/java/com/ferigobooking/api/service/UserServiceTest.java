package com.ferigobooking.api.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.ferigobooking.api.config.Constants;
import com.ferigobooking.api.domain.Authority;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.repository.AuthorityRepository;
import com.ferigobooking.api.repository.UserRepository;
import com.ferigobooking.api.security.AuthoritiesConstants;
import com.ferigobooking.api.security.SecurityUtils;
import com.ferigobooking.api.service.dto.AdminUserDTO;
import com.ferigobooking.api.service.dto.UserDTO;
import java.time.Instant;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.CacheManager;
import org.springframework.cache.Cache;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private AuthorityRepository authorityRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private CacheManager cacheManager;

    @Mock
    private Cache cache;

    @InjectMocks
    private UserService userService;

    private User testUser;
    private Authority userAuthority;
    private Authority adminAuthority;

    @BeforeEach
    void setUp() {
        // Setup test authorities
        userAuthority = new Authority();
        userAuthority.setName(AuthoritiesConstants.USER);

        adminAuthority = new Authority();
        adminAuthority.setName(AuthoritiesConstants.ADMIN);

        // Setup test user
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setLangKey("en");
        testUser.setSupabaseUserId("supabase-123");
        testUser.setAuthorities(Set.of(userAuthority));
        testUser.setCreatedDate(Instant.now());
        testUser.setLastModifiedDate(Instant.now());
    }

    @Test
    void createUser_ShouldCreateUserWithDefaultLanguage() {
        // Given
        AdminUserDTO userDTO = new AdminUserDTO();
        userDTO.setEmail("<EMAIL>");
        userDTO.setFirstName("New");
        userDTO.setLastName("User");
        userDTO.setAuthorities(Set.of(AuthoritiesConstants.USER));

        when(passwordEncoder.encode(anyString())).thenReturn("encoded-password");
        when(authorityRepository.findById(AuthoritiesConstants.USER)).thenReturn(Optional.of(userAuthority));
        when(userRepository.save(any(User.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(cacheManager.getCache(UserRepository.USERS_BY_EMAIL_CACHE)).thenReturn(cache);

        // When
        User result = userService.createUser(userDTO);

        // Then
        assertThat(result.getEmail()).isEqualTo("<EMAIL>");
        assertThat(result.getFirstName()).isEqualTo("New");
        assertThat(result.getLastName()).isEqualTo("User");
        assertThat(result.getLangKey()).isEqualTo(Constants.DEFAULT_LANGUAGE);
        assertThat(result.getAuthorities()).containsExactly(userAuthority);
        verify(userRepository).save(any(User.class));
        verify(cache).evictIfPresent("<EMAIL>");
    }

    @Test
    void createUser_ShouldUseProvidedLanguageKey() {
        // Given
        AdminUserDTO userDTO = new AdminUserDTO();
        userDTO.setEmail("<EMAIL>");
        userDTO.setFirstName("New");
        userDTO.setLastName("User");
        userDTO.setLangKey("id");
        userDTO.setAuthorities(Set.of(AuthoritiesConstants.USER));

        when(passwordEncoder.encode(anyString())).thenReturn("encoded-password");
        when(authorityRepository.findById(AuthoritiesConstants.USER)).thenReturn(Optional.of(userAuthority));
        when(userRepository.save(any(User.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(cacheManager.getCache(UserRepository.USERS_BY_EMAIL_CACHE)).thenReturn(cache);

        // When
        User result = userService.createUser(userDTO);

        // Then
        assertThat(result.getLangKey()).isEqualTo("id");
    }

    @Test
    void createUser_ShouldConvertEmailToLowerCase() {
        // Given
        AdminUserDTO userDTO = new AdminUserDTO();
        userDTO.setEmail("<EMAIL>");
        userDTO.setFirstName("New");
        userDTO.setLastName("User");
        userDTO.setAuthorities(Set.of(AuthoritiesConstants.USER));

        when(passwordEncoder.encode(anyString())).thenReturn("encoded-password");
        when(authorityRepository.findById(AuthoritiesConstants.USER)).thenReturn(Optional.of(userAuthority));
        when(userRepository.save(any(User.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(cacheManager.getCache(UserRepository.USERS_BY_EMAIL_CACHE)).thenReturn(cache);

        // When
        User result = userService.createUser(userDTO);

        // Then
        assertThat(result.getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void updateUser_ShouldUpdateExistingUser() {
        // Given
        AdminUserDTO userDTO = new AdminUserDTO();
        userDTO.setId(1L);
        userDTO.setEmail("<EMAIL>");
        userDTO.setFirstName("Updated");
        userDTO.setLastName("User");
        userDTO.setLangKey("id");
        userDTO.setAuthorities(Set.of(AuthoritiesConstants.ADMIN));

        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(authorityRepository.findById(AuthoritiesConstants.ADMIN)).thenReturn(Optional.of(adminAuthority));
        when(userRepository.save(any(User.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(cacheManager.getCache(UserRepository.USERS_BY_EMAIL_CACHE)).thenReturn(cache);

        // When
        Optional<AdminUserDTO> result = userService.updateUser(userDTO);

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
        assertThat(result.get().getFirstName()).isEqualTo("Updated");
        assertThat(result.get().getLastName()).isEqualTo("User");
        assertThat(result.get().getLangKey()).isEqualTo("id");
        assertThat(result.get().getAuthorities()).containsExactly(AuthoritiesConstants.ADMIN);
        verify(cache, times(2)).evictIfPresent(anyString()); // Called twice for cache clearing
    }

    @Test
    void updateUser_ShouldReturnEmptyWhenUserNotFound() {
        // Given
        AdminUserDTO userDTO = new AdminUserDTO();
        userDTO.setId(999L);

        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // When
        Optional<AdminUserDTO> result = userService.updateUser(userDTO);

        // Then
        assertThat(result).isEmpty();
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void deleteUser_ShouldDeleteExistingUser() {
        // Given
        String email = "<EMAIL>";
        when(userRepository.findOneByEmailIgnoreCase(email)).thenReturn(Optional.of(testUser));
        when(cacheManager.getCache(UserRepository.USERS_BY_EMAIL_CACHE)).thenReturn(cache);

        // When
        userService.deleteUser(email);

        // Then
        verify(userRepository).delete(testUser);
        verify(cache).evictIfPresent(email);
    }

    @Test
    void deleteUser_ShouldDoNothingWhenUserNotFound() {
        // Given
        String email = "<EMAIL>";
        when(userRepository.findOneByEmailIgnoreCase(email)).thenReturn(Optional.empty());

        // When
        userService.deleteUser(email);

        // Then
        verify(userRepository, never()).delete(any(User.class));
    }

    @Test
    void updateUser_ShouldUpdateCurrentUserInfo() {
        // Given
        String firstName = "Updated";
        String lastName = "Name";
        String email = "<EMAIL>";
        String langKey = "id";
        String imageUrl = "http://example.com/avatar.jpg";

        try (MockedStatic<SecurityUtils> securityUtils = mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserLogin).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneByEmailIgnoreCase("<EMAIL>")).thenReturn(Optional.of(testUser));
            when(userRepository.save(any(User.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(cacheManager.getCache(UserRepository.USERS_BY_EMAIL_CACHE)).thenReturn(cache);

            // When
            userService.updateUser(firstName, lastName, email, langKey, imageUrl);

            // Then
            assertThat(testUser.getFirstName()).isEqualTo(firstName);
            assertThat(testUser.getLastName()).isEqualTo(lastName);
            assertThat(testUser.getEmail()).isEqualTo(email);
            assertThat(testUser.getLangKey()).isEqualTo(langKey);
            assertThat(testUser.getAvatarUrl()).isEqualTo(imageUrl);
            verify(userRepository).save(testUser);
            verify(cache).evictIfPresent(anyString());
        }
    }

    @Test
    void getAllManagedUsers_ShouldReturnPageOfAdminUserDTO() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> userPage = new PageImpl<>(List.of(testUser), pageable, 1);
        when(userRepository.findAll(pageable)).thenReturn(userPage);

        // When
        Page<AdminUserDTO> result = userService.getAllManagedUsers(pageable);

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void getAllPublicUsers_ShouldReturnPageOfUserDTO() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> userPage = new PageImpl<>(List.of(testUser), pageable, 1);
        when(userRepository.findAllByIdNotNull(pageable)).thenReturn(userPage);

        // When
        Page<UserDTO> result = userService.getAllPublicUsers(pageable);

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getId()).isEqualTo(1L);
    }

    @Test
    void getUserWithAuthorities_ShouldReturnCurrentUser() {
        // Given
        try (MockedStatic<SecurityUtils> securityUtils = mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>")).thenReturn(Optional.of(testUser));

            // When
            Optional<User> result = userService.getUserWithAuthorities();

            // Then
            assertThat(result).isPresent();
            assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
        }
    }

    @Test
    void getAuthorities_ShouldReturnAllAuthorityNames() {
        // Given
        List<Authority> authorities = List.of(userAuthority, adminAuthority);
        when(authorityRepository.findAll()).thenReturn(authorities);

        // When
        List<String> result = userService.getAuthorities();

        // Then
        assertThat(result).containsExactlyInAnyOrder(AuthoritiesConstants.USER, AuthoritiesConstants.ADMIN);
    }

    @Test
    void clearUserCaches_ShouldEvictCacheWhenEmailExists() {
        // Given
        testUser.setEmail("<EMAIL>");
        when(cacheManager.getCache(UserRepository.USERS_BY_EMAIL_CACHE)).thenReturn(cache);

        // When - This is tested indirectly through other methods that call clearUserCaches
        userService.createUser(new AdminUserDTO(testUser));

        // Then
        verify(cache).evictIfPresent("<EMAIL>");
    }
}
