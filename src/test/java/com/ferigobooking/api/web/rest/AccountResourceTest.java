package com.ferigobooking.api.web.rest;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ferigobooking.api.domain.Authority;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.repository.UserRepository;
import com.ferigobooking.api.security.AuthoritiesConstants;
import com.ferigobooking.api.security.SecurityUtils;
import com.ferigobooking.api.service.MailService;
import com.ferigobooking.api.service.UserService;
import com.ferigobooking.api.web.rest.vm.ManagedUserVM;
import java.util.Optional;
import java.util.Set;
import java.util.HashSet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

@WebMvcTest(AccountResource.class)
class AccountResourceTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private UserRepository userRepository;

    @MockBean
    private UserService userService;

    @MockBean
    private MailService mailService;

    private User testUser;
    private Authority userAuthority;

    @BeforeEach
    void setUp() {
        userAuthority = new Authority();
        userAuthority.setName(AuthoritiesConstants.USER);

        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setLangKey("en");
        testUser.setSupabaseUserId("supabase-123");
        testUser.setAuthorities(new HashSet<>(Set.of(userAuthority)));
    }

    @Test
    @WithMockUser
    void getAccount_ShouldReturnCurrentUserAccount() throws Exception {
        // Given
        try (MockedStatic<SecurityUtils> securityUtils = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>"))
                .thenReturn(Optional.of(testUser));

            // When & Then
            mockMvc.perform(get("/api/account"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.firstName").value("Test"))
                .andExpect(jsonPath("$.lastName").value("User"))
                .andExpect(jsonPath("$.langKey").value("en"))
                .andExpect(jsonPath("$.authorities[0]").value(AuthoritiesConstants.USER));
        }
    }

    @Test
    @WithMockUser
    void getAccount_ShouldReturnInternalServerErrorWhenUserEmailNotFound() throws Exception {
        // Given
        try (MockedStatic<SecurityUtils> securityUtils = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.empty());

            // When & Then
            mockMvc.perform(get("/api/account"))
                .andExpect(status().isInternalServerError());
        }
    }

    @Test
    @WithMockUser
    void getAccount_ShouldReturnInternalServerErrorWhenUserNotFoundInDatabase() throws Exception {
        // Given
        try (MockedStatic<SecurityUtils> securityUtils = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>"))
                .thenReturn(Optional.empty());

            // When & Then
            mockMvc.perform(get("/api/account"))
                .andExpect(status().isInternalServerError());
        }
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldUpdateUserAccount() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Updated");
        managedUserVM.setLastName("Name");
        managedUserVM.setEmail("<EMAIL>");
        managedUserVM.setLangKey("id");

        try (MockedStatic<SecurityUtils> securityUtils = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>"))
                .thenReturn(Optional.of(testUser));

            // When & Then
            mockMvc.perform(post("/api/account")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(managedUserVM)))
                .andExpect(status().isOk());
        }
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldValidateRequiredFields() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        // Missing required fields

        // When & Then
        mockMvc.perform(post("/api/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(managedUserVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldValidateEmailFormat() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Test");
        managedUserVM.setLastName("User");
        managedUserVM.setEmail("invalid-email");
        managedUserVM.setLangKey("en");

        // When & Then
        mockMvc.perform(post("/api/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(managedUserVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldValidateEmailLength() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Test");
        managedUserVM.setLastName("User");
        managedUserVM.setEmail("a".repeat(255) + "@example.com"); // Too long
        managedUserVM.setLangKey("en");

        // When & Then
        mockMvc.perform(post("/api/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(managedUserVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldValidateFirstNameLength() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("a".repeat(51)); // Too long
        managedUserVM.setLastName("User");
        managedUserVM.setEmail("<EMAIL>");
        managedUserVM.setLangKey("en");

        // When & Then
        mockMvc.perform(post("/api/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(managedUserVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldValidateLastNameLength() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Test");
        managedUserVM.setLastName("a".repeat(51)); // Too long
        managedUserVM.setEmail("<EMAIL>");
        managedUserVM.setLangKey("en");

        // When & Then
        mockMvc.perform(post("/api/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(managedUserVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldValidateLangKeyPattern() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Test");
        managedUserVM.setLastName("User");
        managedUserVM.setEmail("<EMAIL>");
        managedUserVM.setLangKey("invalid-lang-key"); // Invalid pattern

        // When & Then
        mockMvc.perform(post("/api/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(managedUserVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldAcceptValidLangKeys() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Test");
        managedUserVM.setLastName("User");
        managedUserVM.setEmail("<EMAIL>");
        managedUserVM.setLangKey("en");

        try (MockedStatic<SecurityUtils> securityUtils = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>"))
                .thenReturn(Optional.of(testUser));

            // When & Then
            mockMvc.perform(post("/api/account")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(managedUserVM)))
                .andExpect(status().isOk());
        }
    }

    @Test
    @WithMockUser
    void saveAccount_ShouldHandleIndonesianLangKey() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Test");
        managedUserVM.setLastName("User");
        managedUserVM.setEmail("<EMAIL>");
        managedUserVM.setLangKey("id");

        try (MockedStatic<SecurityUtils> securityUtils = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>"))
                .thenReturn(Optional.of(testUser));

            // When & Then
            mockMvc.perform(post("/api/account")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(managedUserVM)))
                .andExpect(status().isOk());
        }
    }

    @Test
    void getAccount_ShouldRequireAuthentication() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/account"))
            .andExpect(status().isUnauthorized());
    }

    @Test
    void saveAccount_ShouldRequireAuthentication() throws Exception {
        // Given
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setFirstName("Test");
        managedUserVM.setLastName("User");
        managedUserVM.setEmail("<EMAIL>");
        managedUserVM.setLangKey("en");

        // When & Then
        mockMvc.perform(post("/api/account")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(managedUserVM)))
            .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser
    void getAccount_ShouldReturnUserWithAllAuthorities() throws Exception {
        // Given
        Authority adminAuthority = new Authority();
        adminAuthority.setName(AuthoritiesConstants.ADMIN);
        testUser.setAuthorities(new HashSet<>(Set.of(userAuthority, adminAuthority)));

        try (MockedStatic<SecurityUtils> securityUtils = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtils.when(SecurityUtils::getCurrentUserEmail).thenReturn(Optional.of("<EMAIL>"));
            when(userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>"))
                .thenReturn(Optional.of(testUser));

            // When & Then
            mockMvc.perform(get("/api/account"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.authorities").isArray())
                .andExpect(jsonPath("$.authorities").value(org.hamcrest.Matchers.hasSize(2)))
                .andExpect(jsonPath("$.authorities").value(org.hamcrest.Matchers.containsInAnyOrder(
                    AuthoritiesConstants.USER, AuthoritiesConstants.ADMIN)));
        }
    }
}
