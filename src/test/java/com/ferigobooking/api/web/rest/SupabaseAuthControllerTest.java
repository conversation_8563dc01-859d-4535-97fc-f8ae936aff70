package com.ferigobooking.api.web.rest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.service.SupabaseAuthService;
import com.ferigobooking.api.service.dto.AuthTokensDTO;
import com.ferigobooking.api.web.rest.vm.SupabaseRefreshTokenVM;
import com.ferigobooking.api.web.rest.vm.SupabaseSignInVM;
import com.ferigobooking.api.web.rest.vm.SupabaseSignUpVM;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import reactor.core.publisher.Mono;

@WebMvcTest(SupabaseAuthController.class)
class SupabaseAuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private SupabaseAuthService supabaseAuthService;

    private AuthTokensDTO authTokens;
    private User testUser;

    @BeforeEach
    void setUp() {
        authTokens = new AuthTokensDTO();
        authTokens.setAccessToken("access_token_123");
        authTokens.setRefreshToken("refresh_token_123");

        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
    }

    @Test
    void signIn_ShouldReturnAuthResponseWithTokens() throws Exception {
        // Given
        SupabaseSignInVM signInVM = new SupabaseSignInVM();
        signInVM.setEmail("<EMAIL>");
        signInVM.setPassword("password123");

        when(supabaseAuthService.signInWithEmailPassword(anyString(), anyString()))
            .thenReturn(Mono.just(authTokens));
        when(supabaseAuthService.getUserFromSupabase(anyString()))
            .thenReturn(Mono.just(testUser));

        // When & Then
        mockMvc.perform(post("/api/auth/signin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signInVM)))
            .andExpect(status().isOk())
            .andExpect(header().string("Authorization", "Bearer access_token_123"))
            .andExpect(jsonPath("$.accessToken").value("access_token_123"))
            .andExpect(jsonPath("$.refreshToken").value("refresh_token_123"));
    }

    @Test
    void signIn_ShouldReturnUnauthorizedOnError() throws Exception {
        // Given
        SupabaseSignInVM signInVM = new SupabaseSignInVM();
        signInVM.setEmail("<EMAIL>");
        signInVM.setPassword("wrong_password");

        when(supabaseAuthService.signInWithEmailPassword(anyString(), anyString()))
            .thenReturn(Mono.error(new RuntimeException("Invalid credentials")));

        // When & Then
        mockMvc.perform(post("/api/auth/signin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signInVM)))
            .andExpect(status().isUnauthorized());
    }

    @Test
    void signIn_ShouldValidateRequiredFields() throws Exception {
        // Given
        SupabaseSignInVM signInVM = new SupabaseSignInVM();
        // Missing email and password

        // When & Then
        mockMvc.perform(post("/api/auth/signin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signInVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    void signUp_ShouldReturnAuthResponseWithTokens() throws Exception {
        // Given
        SupabaseSignUpVM signUpVM = new SupabaseSignUpVM();
        signUpVM.setEmail("<EMAIL>");
        signUpVM.setPassword("password123");
        signUpVM.setFirstName("New");
        signUpVM.setLastName("User");

        when(supabaseAuthService.signUpWithEmailPassword(anyString(), anyString(), anyString(), anyString()))
            .thenReturn(Mono.just(authTokens));
        when(supabaseAuthService.getUserFromSupabase(anyString()))
            .thenReturn(Mono.just(testUser));

        // When & Then
        mockMvc.perform(post("/api/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signUpVM)))
            .andExpect(status().isCreated())
            .andExpect(header().string("Authorization", "Bearer access_token_123"))
            .andExpect(jsonPath("$.accessToken").value("access_token_123"))
            .andExpect(jsonPath("$.refreshToken").value("refresh_token_123"));
    }

    @Test
    void signUp_ShouldReturnBadRequestOnError() throws Exception {
        // Given
        SupabaseSignUpVM signUpVM = new SupabaseSignUpVM();
        signUpVM.setEmail("<EMAIL>");
        signUpVM.setPassword("password123");
        signUpVM.setFirstName("New");
        signUpVM.setLastName("User");

        when(supabaseAuthService.signUpWithEmailPassword(anyString(), anyString(), anyString(), anyString()))
            .thenReturn(Mono.error(new RuntimeException("Email already exists")));

        // When & Then
        mockMvc.perform(post("/api/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signUpVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    void signUp_ShouldValidateRequiredFields() throws Exception {
        // Given
        SupabaseSignUpVM signUpVM = new SupabaseSignUpVM();
        // Missing required fields

        // When & Then
        mockMvc.perform(post("/api/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signUpVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    void signOut_ShouldReturnOkOnSuccess() throws Exception {
        // Given
        when(supabaseAuthService.signOut(anyString()))
            .thenReturn(Mono.empty());

        // When & Then
        mockMvc.perform(post("/api/auth/signout")
                .header("Authorization", "Bearer access_token_123"))
            .andExpect(status().isOk());
    }

    @Test
    void signOut_ShouldReturnInternalServerErrorOnFailure() throws Exception {
        // Given
        when(supabaseAuthService.signOut(anyString()))
            .thenReturn(Mono.error(new RuntimeException("Signout failed")));

        // When & Then
        mockMvc.perform(post("/api/auth/signout")
                .header("Authorization", "Bearer access_token_123"))
            .andExpect(status().isInternalServerError());
    }

    @Test
    void refreshToken_ShouldReturnNewTokens() throws Exception {
        // Given
        SupabaseRefreshTokenVM refreshTokenVM = new SupabaseRefreshTokenVM();
        refreshTokenVM.setRefreshToken("refresh_token_123");

        AuthTokensDTO newTokens = new AuthTokensDTO();
        newTokens.setAccessToken("new_access_token");
        newTokens.setRefreshToken("new_refresh_token");

        when(supabaseAuthService.refreshToken(anyString()))
            .thenReturn(Mono.just(newTokens));

        // When & Then
        mockMvc.perform(post("/api/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(refreshTokenVM)))
            .andExpect(status().isOk())
            .andExpect(header().string("Authorization", "Bearer new_access_token"))
            .andExpect(jsonPath("$.accessToken").value("new_access_token"))
            .andExpect(jsonPath("$.refreshToken").value("new_refresh_token"));
    }

    @Test
    void refreshToken_ShouldReturnUnauthorizedOnError() throws Exception {
        // Given
        SupabaseRefreshTokenVM refreshTokenVM = new SupabaseRefreshTokenVM();
        refreshTokenVM.setRefreshToken("invalid_refresh_token");

        when(supabaseAuthService.refreshToken(anyString()))
            .thenReturn(Mono.error(new RuntimeException("Invalid refresh token")));

        // When & Then
        mockMvc.perform(post("/api/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(refreshTokenVM)))
            .andExpect(status().isUnauthorized());
    }

    @Test
    void refreshToken_ShouldValidateRequiredFields() throws Exception {
        // Given
        SupabaseRefreshTokenVM refreshTokenVM = new SupabaseRefreshTokenVM();
        // Missing refresh token

        // When & Then
        mockMvc.perform(post("/api/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(refreshTokenVM)))
            .andExpect(status().isBadRequest());
    }

    @Test
    void signIn_ShouldHandleEmailCaseInsensitive() throws Exception {
        // Given
        SupabaseSignInVM signInVM = new SupabaseSignInVM();
        signInVM.setEmail("<EMAIL>");
        signInVM.setPassword("password123");

        when(supabaseAuthService.signInWithEmailPassword(anyString(), anyString()))
            .thenReturn(Mono.just(authTokens));
        when(supabaseAuthService.getUserFromSupabase(anyString()))
            .thenReturn(Mono.just(testUser));

        // When & Then
        mockMvc.perform(post("/api/auth/signin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signInVM)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.accessToken").value("access_token_123"));
    }

    @Test
    void signUp_ShouldHandleOptionalNames() throws Exception {
        // Given
        SupabaseSignUpVM signUpVM = new SupabaseSignUpVM();
        signUpVM.setEmail("<EMAIL>");
        signUpVM.setPassword("password123");
        // firstName and lastName are optional

        when(supabaseAuthService.signUpWithEmailPassword(anyString(), anyString(), any(), any()))
            .thenReturn(Mono.just(authTokens));
        when(supabaseAuthService.getUserFromSupabase(anyString()))
            .thenReturn(Mono.just(testUser));

        // When & Then
        mockMvc.perform(post("/api/auth/signup")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signUpVM)))
            .andExpect(status().isCreated())
            .andExpect(jsonPath("$.accessToken").value("access_token_123"));
    }
}
