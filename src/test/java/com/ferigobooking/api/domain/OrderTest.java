package com.ferigobooking.api.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.ferigobooking.api.domain.enumeration.OrderStatus;
import com.ferigobooking.api.domain.enumeration.PaymentMethod;
import com.ferigobooking.api.domain.enumeration.PaymentStatus;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;

class OrderTest {

    @Test
    void testOrderCreation() {
        // Given
        Order order = new Order();
        order.setStatus(OrderStatus.PLACED);
        order.setTotalPrice(new BigDecimal("150.00"));
        order.setTotalDiscount(new BigDecimal("15.00"));
        order.setChargedAmount(new BigDecimal("135.00"));
        order.setTotalFee(new BigDecimal("10.00"));
        order.setOrganiserName("Jane Smith");
        order.setOrganiserPhone("+1234567890");
        order.setOrganiserEmail("<EMAIL>");
        order.setNotes("Special requirements");

        // Then
        assertThat(order.getStatus()).isEqualTo(OrderStatus.PLACED);
        assertThat(order.getTotalPrice()).isEqualByComparingTo(new BigDecimal("150.00"));
        assertThat(order.getTotalDiscount()).isEqualByComparingTo(new BigDecimal("15.00"));
        assertThat(order.getChargedAmount()).isEqualByComparingTo(new BigDecimal("135.00"));
        assertThat(order.getTotalFee()).isEqualByComparingTo(new BigDecimal("10.00"));
        assertThat(order.getOrganiserName()).isEqualTo("Jane Smith");
        assertThat(order.getOrganiserPhone()).isEqualTo("+1234567890");
        assertThat(order.getOrganiserEmail()).isEqualTo("<EMAIL>");
        assertThat(order.getNotes()).isEqualTo("Special requirements");
    }

    @Test
    void testOrderPriceCalculations() {
        // Given
        Order order = new Order();
        BigDecimal basePrice = new BigDecimal("100.00");
        BigDecimal discount = new BigDecimal("10.00");
        BigDecimal fee = new BigDecimal("5.00");
        
        order.setTotalPrice(basePrice);
        order.setTotalDiscount(discount);
        order.setTotalFee(fee);
        
        // Calculate charged amount: totalPrice - discount + fee
        BigDecimal expectedChargedAmount = basePrice.subtract(discount).add(fee);
        order.setChargedAmount(expectedChargedAmount);

        // Then
        assertThat(order.getTotalPrice()).isEqualByComparingTo(basePrice);
        assertThat(order.getTotalDiscount()).isEqualByComparingTo(discount);
        assertThat(order.getTotalFee()).isEqualByComparingTo(fee);
        assertThat(order.getChargedAmount()).isEqualByComparingTo(new BigDecimal("95.00"));
    }

    @Test
    void testOrderStatusTransitions() {
        // Given
        Order order = new Order();
        
        // Test valid status transitions
        order.setStatus(OrderStatus.PLACED);
        assertThat(order.getStatus()).isEqualTo(OrderStatus.PLACED);

        order.setStatus(OrderStatus.PROCESSING);
        assertThat(order.getStatus()).isEqualTo(OrderStatus.PROCESSING);

        order.setStatus(OrderStatus.DELIVERED);
        assertThat(order.getStatus()).isEqualTo(OrderStatus.DELIVERED);

        // Test other statuses
        order.setStatus(OrderStatus.CANCELLED);
        assertThat(order.getStatus()).isEqualTo(OrderStatus.CANCELLED);

        order.setStatus(OrderStatus.RETURNED);
        assertThat(order.getStatus()).isEqualTo(OrderStatus.RETURNED);

        order.setStatus(OrderStatus.REFUNDED);
        assertThat(order.getStatus()).isEqualTo(OrderStatus.REFUNDED);
    }

    @Test
    void testOrderWithPayment() {
        // Given
        Order order = new Order();
        order.setStatus(OrderStatus.PLACED);
        order.setChargedAmount(new BigDecimal("100.00"));

        Payment payment = new Payment();
        payment.setStatus(PaymentStatus.COMPLETED);
        payment.setAmountPaid(new BigDecimal("100.00"));
        payment.setPaymentMethod(PaymentMethod.CREDIT_CARD);
        payment.setOrder(order);

        order.setPayment(payment);

        // Then
        assertThat(order.getPayment()).isNotNull();
        assertThat(order.getPayment().getStatus()).isEqualTo(PaymentStatus.COMPLETED);
        assertThat(order.getPayment().getAmountPaid()).isEqualByComparingTo(order.getChargedAmount());
        assertThat(order.getPayment().getPaymentMethod()).isEqualTo(PaymentMethod.CREDIT_CARD);
    }

    @Test
    void testOrderWithBooking() {
        // Given
        Order order = new Order();
        order.setStatus(OrderStatus.PLACED);

        Booking booking = new Booking();
        booking.setCode("BK001");
        booking.setOrganiserFullname("John Doe");
        booking.setOrganiserEmail("<EMAIL>");
        booking.setOrganiserPhone("+1234567890");
        booking.setOrder(order);

        order.setBooking(booking);

        // Then
        assertThat(order.getBooking()).isNotNull();
        assertThat(order.getBooking().getCode()).isEqualTo("BK001");
        assertThat(order.getBooking().getOrganiserFullname()).isEqualTo("John Doe");
    }

    @Test
    void testOrderWithCustomerUser() {
        // Given
        Order order = new Order();
        
        User customer = new User();
        customer.setId(1L);
        customer.setEmail("<EMAIL>");
        customer.setFirstName("Customer");
        customer.setLastName("User");

        order.setCustomerUserRef(customer);

        // Then
        assertThat(order.getCustomerUserRef()).isNotNull();
        assertThat(order.getCustomerUserRef().getEmail()).isEqualTo("<EMAIL>");
        assertThat(order.getCustomerUserRef().getFirstName()).isEqualTo("Customer");
    }

    @Test
    void testOrderPriceValidation() {
        // Given
        Order order = new Order();
        
        // Test zero amounts
        order.setTotalPrice(BigDecimal.ZERO);
        order.setTotalDiscount(BigDecimal.ZERO);
        order.setTotalFee(BigDecimal.ZERO);
        order.setChargedAmount(BigDecimal.ZERO);

        // Then
        assertThat(order.getTotalPrice()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(order.getTotalDiscount()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(order.getTotalFee()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(order.getChargedAmount()).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    void testOrderWithLargeAmounts() {
        // Given
        Order order = new Order();
        BigDecimal largeAmount = new BigDecimal("999999.99");
        
        order.setTotalPrice(largeAmount);
        order.setTotalDiscount(new BigDecimal("99999.99"));
        order.setTotalFee(new BigDecimal("50000.00"));
        order.setChargedAmount(new BigDecimal("950000.00"));

        // Then
        assertThat(order.getTotalPrice()).isEqualByComparingTo(largeAmount);
        assertThat(order.getTotalDiscount()).isEqualByComparingTo(new BigDecimal("99999.99"));
        assertThat(order.getTotalFee()).isEqualByComparingTo(new BigDecimal("50000.00"));
        assertThat(order.getChargedAmount()).isEqualByComparingTo(new BigDecimal("950000.00"));
    }

    @Test
    void testOrderIdentity() {
        // Given
        Order order1 = new Order();
        order1.setId(1L);
        order1.setStatus(OrderStatus.PLACED);

        Order order2 = new Order();
        order2.setId(1L);
        order2.setStatus(OrderStatus.PLACED);

        // Then - Test object identity
        assertThat(order1.getId()).isEqualTo(order2.getId());
        assertThat(order1.getStatus()).isEqualTo(order2.getStatus());
    }

    @Test
    void testOrderStringRepresentation() {
        // Given
        Order order = new Order();
        order.setId(1L);
        order.setStatus(OrderStatus.PLACED);
        order.setOrganiserName("John Doe");

        // When
        String toString = order.toString();

        // Then
        assertThat(toString).isNotNull();
        assertThat(toString).contains("Order");
    }

    @Test
    void testOrderEmailValidation() {
        // Given
        Order order = new Order();
        
        // Test valid email formats
        order.setOrganiserEmail("<EMAIL>");
        assertThat(order.getOrganiserEmail()).isEqualTo("<EMAIL>");

        order.setOrganiserEmail("<EMAIL>");
        assertThat(order.getOrganiserEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void testOrderBusinessRules() {
        // Given
        Order order = new Order();
        order.setTotalPrice(new BigDecimal("100.00"));
        order.setTotalDiscount(new BigDecimal("20.00"));
        order.setTotalFee(new BigDecimal("5.00"));
        
        // Business rule: charged amount should be total price - discount + fee
        BigDecimal expectedChargedAmount = order.getTotalPrice()
            .subtract(order.getTotalDiscount())
            .add(order.getTotalFee());
        
        order.setChargedAmount(expectedChargedAmount);

        // Then
        assertThat(order.getChargedAmount()).isEqualByComparingTo(new BigDecimal("85.00"));
    }

    @Test
    void testOrderWithNegativeDiscount() {
        // Given
        Order order = new Order();
        order.setTotalPrice(new BigDecimal("100.00"));
        order.setTotalDiscount(new BigDecimal("-10.00")); // Negative discount (surcharge)
        order.setTotalFee(new BigDecimal("5.00"));
        
        // Calculate charged amount with negative discount
        BigDecimal expectedChargedAmount = order.getTotalPrice()
            .subtract(order.getTotalDiscount())
            .add(order.getTotalFee());
        
        order.setChargedAmount(expectedChargedAmount);

        // Then - Negative discount increases the charged amount
        assertThat(order.getChargedAmount()).isEqualByComparingTo(new BigDecimal("115.00"));
    }

    @Test
    void testOrderNotesHandling() {
        // Given
        Order order = new Order();
        
        // Test empty notes
        order.setNotes("");
        assertThat(order.getNotes()).isEmpty();

        // Test null notes
        order.setNotes(null);
        assertThat(order.getNotes()).isNull();

        // Test long notes
        String longNotes = "This is a very long note that contains special requirements for the ferry booking including dietary restrictions, wheelchair accessibility, and special assistance needed during boarding and disembarking.";
        order.setNotes(longNotes);
        assertThat(order.getNotes()).isEqualTo(longNotes);
    }
}
