package com.ferigobooking.api.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.ferigobooking.api.domain.enumeration.BookingStatus;
import com.ferigobooking.api.domain.enumeration.OrderStatus;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;

class BookingTest {

    @Test
    void testBookingCreation() {
        // Given
        Booking booking = new Booking();
        booking.setCode("BK001");
        booking.setOrganiserFullname("John <PERSON>");
        booking.setOrganiserEmail("<EMAIL>");
        booking.setOrganiserPhone("+1234567890");
        booking.setIsRoundTrip(true);
        booking.setIsOpenReturn(false);
        booking.setStatus(BookingStatus.PENDING);
        booking.setOpConfirmationCode("OP123");

        // Then
        assertThat(booking.getCode()).isEqualTo("BK001");
        assertThat(booking.getOrganiserFullname()).isEqualTo("John <PERSON>e");
        assertThat(booking.getOrganiserEmail()).isEqualTo("<EMAIL>");
        assertThat(booking.getOrganiserPhone()).isEqualTo("+1234567890");
        assertThat(booking.getIsRoundTrip()).isTrue();
        assertThat(booking.getIsOpenReturn()).isFalse();
        assertThat(booking.getStatus()).isEqualTo(BookingStatus.PENDING);
        assertThat(booking.getOpConfirmationCode()).isEqualTo("OP123");
    }

    @Test
    void testBookingWithOrder() {
        // Given
        Booking booking = new Booking();
        booking.setCode("BK001");
        booking.setOrganiserFullname("John Doe");
        booking.setOrganiserEmail("<EMAIL>");
        booking.setOrganiserPhone("+1234567890");
        booking.setIsRoundTrip(true);
        booking.setIsOpenReturn(false);
        booking.setStatus(BookingStatus.PENDING);

        Order order = new Order();
        order.setStatus(OrderStatus.PLACED);
        order.setTotalPrice(new BigDecimal("100.00"));
        order.setTotalDiscount(new BigDecimal("10.00"));
        order.setChargedAmount(new BigDecimal("90.00"));
        order.setTotalFee(new BigDecimal("5.00"));
        order.setOrganiserName("John Doe");
        order.setOrganiserPhone("+1234567890");
        order.setOrganiserEmail("<EMAIL>");

        booking.setOrder(order);

        // Then
        assertThat(booking.getOrder()).isNotNull();
        assertThat(booking.getOrder().getStatus()).isEqualTo(OrderStatus.PLACED);
        assertThat(booking.getOrder().getTotalPrice()).isEqualByComparingTo(new BigDecimal("100.00"));
        assertThat(booking.getOrder().getChargedAmount()).isEqualByComparingTo(new BigDecimal("90.00"));
    }

    @Test
    void testBookingStatusTransitions() {
        // Given
        Booking booking = new Booking();
        booking.setStatus(BookingStatus.PENDING);

        // When & Then - Valid transitions
        booking.setStatus(BookingStatus.PROCESSING);
        assertThat(booking.getStatus()).isEqualTo(BookingStatus.PROCESSING);

        booking.setStatus(BookingStatus.CONFIRMED);
        assertThat(booking.getStatus()).isEqualTo(BookingStatus.CONFIRMED);

        // Test other statuses
        booking.setStatus(BookingStatus.REJECTED);
        assertThat(booking.getStatus()).isEqualTo(BookingStatus.REJECTED);

        booking.setStatus(BookingStatus.FAILED);
        assertThat(booking.getStatus()).isEqualTo(BookingStatus.FAILED);

        booking.setStatus(BookingStatus.CANCELLED);
        assertThat(booking.getStatus()).isEqualTo(BookingStatus.CANCELLED);
    }

    @Test
    void testBookingEqualsAndHashCode() {
        // Given
        Booking booking1 = new Booking();
        booking1.setId(1L);
        booking1.setCode("BK001");

        Booking booking2 = new Booking();
        booking2.setId(1L);
        booking2.setCode("BK001");

        Booking booking3 = new Booking();
        booking3.setId(2L);
        booking3.setCode("BK002");

        // Then
        assertThat(booking1).isEqualTo(booking2);
        assertThat(booking1).isNotEqualTo(booking3);
        assertThat(booking1.hashCode()).isEqualTo(booking2.hashCode());
    }

    @Test
    void testBookingToString() {
        // Given
        Booking booking = new Booking();
        booking.setId(1L);
        booking.setCode("BK001");
        booking.setOrganiserFullname("John Doe");

        // When
        String toString = booking.toString();

        // Then
        assertThat(toString).contains("Booking");
        assertThat(toString).contains("id=1");
    }

    @Test
    void testRoundTripBookingBusinessRules() {
        // Given
        Booking roundTripBooking = new Booking();
        roundTripBooking.setIsRoundTrip(true);
        roundTripBooking.setIsOpenReturn(false);

        // Then - Round trip booking should not be open return
        assertThat(roundTripBooking.getIsRoundTrip()).isTrue();
        assertThat(roundTripBooking.getIsOpenReturn()).isFalse();
    }

    @Test
    void testOneWayBookingBusinessRules() {
        // Given
        Booking oneWayBooking = new Booking();
        oneWayBooking.setIsRoundTrip(false);
        oneWayBooking.setIsOpenReturn(false);

        // Then - One way booking
        assertThat(oneWayBooking.getIsRoundTrip()).isFalse();
        assertThat(oneWayBooking.getIsOpenReturn()).isFalse();
    }

    @Test
    void testOpenReturnBookingBusinessRules() {
        // Given
        Booking openReturnBooking = new Booking();
        openReturnBooking.setIsRoundTrip(true);
        openReturnBooking.setIsOpenReturn(true);

        // Then - Open return can be combined with round trip
        assertThat(openReturnBooking.getIsRoundTrip()).isTrue();
        assertThat(openReturnBooking.getIsOpenReturn()).isTrue();
    }

    @Test
    void testBookingWithOperator() {
        // Given
        Booking booking = new Booking();
        Operator operator = new Operator();
        operator.setId(1L);
        operator.setName("Ferry Operator");
        operator.setCode("FO001");

        booking.setOperator(operator);

        // Then
        assertThat(booking.getOperator()).isNotNull();
        assertThat(booking.getOperator().getName()).isEqualTo("Ferry Operator");
        assertThat(booking.getOperator().getCode()).isEqualTo("FO001");
    }

    @Test
    void testBookingWithOrganiser() {
        // Given
        Booking booking = new Booking();
        Organiser organiser = new Organiser();
        organiser.setId(1L);
        organiser.setFullName("John Doe");
        organiser.setEmail("<EMAIL>");
        organiser.setPhone("+1234567890");

        booking.setOrganiser(organiser);

        // Then
        assertThat(booking.getOrganiser()).isNotNull();
        assertThat(booking.getOrganiser().getFullName()).isEqualTo("John Doe");
        assertThat(booking.getOrganiser().getEmail()).isEqualTo("<EMAIL>");
        assertThat(booking.getOrganiser().getPhone()).isEqualTo("+1234567890");
    }

    @Test
    void testBookingEmailValidation() {
        // Given
        Booking booking = new Booking();
        
        // Test valid email formats
        booking.setOrganiserEmail("<EMAIL>");
        assertThat(booking.getOrganiserEmail()).isEqualTo("<EMAIL>");

        booking.setOrganiserEmail("<EMAIL>");
        assertThat(booking.getOrganiserEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void testBookingPhoneNumberFormats() {
        // Given
        Booking booking = new Booking();
        
        // Test various phone number formats
        booking.setOrganiserPhone("+62812345678");
        assertThat(booking.getOrganiserPhone()).isEqualTo("+62812345678");

        booking.setOrganiserPhone("08123456789");
        assertThat(booking.getOrganiserPhone()).isEqualTo("08123456789");

        booking.setOrganiserPhone("******-123-4567");
        assertThat(booking.getOrganiserPhone()).isEqualTo("******-123-4567");
    }

    @Test
    void testBookingCodeFormat() {
        // Given
        Booking booking = new Booking();
        
        // Test booking code formats
        booking.setCode("BK20250101001");
        assertThat(booking.getCode()).isEqualTo("BK20250101001");

        booking.setCode("FERRY-001");
        assertThat(booking.getCode()).isEqualTo("FERRY-001");
    }
}
