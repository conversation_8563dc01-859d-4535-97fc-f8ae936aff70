package com.ferigobooking.api.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ferigobooking.api.domain.Authority;
import com.ferigobooking.api.domain.PromoCode;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.security.AuthoritiesConstants;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.HashSet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

@DataJpaTest
@ActiveProfiles("test")
class PromoCodeRepositoryWithBagRelationshipsImplTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private PromoCodeRepository promoCodeRepository;

    private PromoCode testPromoCode;
    private User testUser1;
    private User testUser2;
    private Authority userAuthority;

    @BeforeEach
    void setUp() {
        // Create and persist authority
        userAuthority = new Authority();
        userAuthority.setName(AuthoritiesConstants.USER);
        entityManager.persistAndFlush(userAuthority);

        // Create test users
        testUser1 = new User();
        testUser1.setEmail("<EMAIL>");
        testUser1.setFirstName("User");
        testUser1.setLastName("One");
        testUser1.setLangKey("en");
        testUser1.setSupabaseUserId("supabase-1");
        testUser1.setPassword("encoded-password");
        testUser1.setAuthorities(new HashSet<>(Set.of(userAuthority)));
        testUser1.setCreatedDate(Instant.now());
        testUser1.setLastModifiedDate(Instant.now());
        entityManager.persistAndFlush(testUser1);

        testUser2 = new User();
        testUser2.setEmail("<EMAIL>");
        testUser2.setFirstName("User");
        testUser2.setLastName("Two");
        testUser2.setLangKey("en");
        testUser2.setSupabaseUserId("supabase-2");
        testUser2.setPassword("encoded-password");
        testUser2.setAuthorities(new HashSet<>(Set.of(userAuthority)));
        testUser2.setCreatedDate(Instant.now());
        testUser2.setLastModifiedDate(Instant.now());
        entityManager.persistAndFlush(testUser2);

        // Create test promo code
        testPromoCode = new PromoCode();
        testPromoCode.setCode("TESTPROMO");
        testPromoCode.setDescription("Test Promo Code");
        testPromoCode.setDescriptionEn("Test Promo Code");
        testPromoCode.setIsActive(true);
        testPromoCode.setTargetUsers(Set.of(testUser1, testUser2));
        entityManager.persistAndFlush(testPromoCode);
    }

    @Test
    void fetchBagRelationships_ShouldLoadTargetUsersForOptionalPromoCode() {
        // Given
        entityManager.clear(); // Clear persistence context to ensure lazy loading
        Optional<PromoCode> promoCodeOptional = promoCodeRepository.findById(testPromoCode.getId());

        // When
        Optional<PromoCode> result = promoCodeRepository.fetchBagRelationships(promoCodeOptional);

        // Then
        assertThat(result).isPresent();
        PromoCode promoCode = result.get();
        assertThat(promoCode.getTargetUsers()).hasSize(2);
        assertThat(promoCode.getTargetUsers()).extracting(User::getEmail)
            .containsExactlyInAnyOrder("<EMAIL>", "<EMAIL>");
    }

    @Test
    void fetchBagRelationships_ShouldReturnEmptyWhenOptionalIsEmpty() {
        // Given
        Optional<PromoCode> emptyOptional = Optional.empty();

        // When
        Optional<PromoCode> result = promoCodeRepository.fetchBagRelationships(emptyOptional);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void fetchBagRelationships_ShouldLoadTargetUsersForListOfPromoCodes() {
        // Given
        PromoCode anotherPromoCode = new PromoCode();
        anotherPromoCode.setCode("ANOTHER");
        anotherPromoCode.setDescription("Another Promo");
        anotherPromoCode.setDescriptionEn("Another Promo");
        anotherPromoCode.setIsActive(true);
        anotherPromoCode.setTargetUsers(Set.of(testUser1));
        entityManager.persistAndFlush(anotherPromoCode);

        entityManager.clear(); // Clear persistence context
        List<PromoCode> promoCodes = List.of(testPromoCode, anotherPromoCode);

        // When
        List<PromoCode> result = promoCodeRepository.fetchBagRelationships(promoCodes);

        // Then
        assertThat(result).hasSize(2);
        
        PromoCode firstPromo = result.stream()
            .filter(pc -> pc.getCode().equals("TESTPROMO"))
            .findFirst()
            .orElseThrow();
        assertThat(firstPromo.getTargetUsers()).hasSize(2);

        PromoCode secondPromo = result.stream()
            .filter(pc -> pc.getCode().equals("ANOTHER"))
            .findFirst()
            .orElseThrow();
        assertThat(secondPromo.getTargetUsers()).hasSize(1);
        assertThat(secondPromo.getTargetUsers().iterator().next().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void fetchBagRelationships_ShouldReturnEmptyListWhenInputIsEmpty() {
        // Given
        List<PromoCode> emptyList = List.of();

        // When
        List<PromoCode> result = promoCodeRepository.fetchBagRelationships(emptyList);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void fetchBagRelationships_ShouldLoadTargetUsersForPageOfPromoCodes() {
        // Given
        PromoCode anotherPromoCode = new PromoCode();
        anotherPromoCode.setCode("ANOTHER");
        anotherPromoCode.setDescription("Another Promo");
        anotherPromoCode.setDescriptionEn("Another Promo");
        anotherPromoCode.setIsActive(true);
        anotherPromoCode.setTargetUsers(Set.of(testUser2));
        entityManager.persistAndFlush(anotherPromoCode);

        entityManager.clear(); // Clear persistence context
        
        Pageable pageable = PageRequest.of(0, 10);
        Page<PromoCode> promoCodePage = new PageImpl<>(
            List.of(testPromoCode, anotherPromoCode), 
            pageable, 
            2
        );

        // When
        Page<PromoCode> result = promoCodeRepository.fetchBagRelationships(promoCodePage);

        // Then
        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getPageable()).isEqualTo(pageable);

        PromoCode firstPromo = result.getContent().stream()
            .filter(pc -> pc.getCode().equals("TESTPROMO"))
            .findFirst()
            .orElseThrow();
        assertThat(firstPromo.getTargetUsers()).hasSize(2);

        PromoCode secondPromo = result.getContent().stream()
            .filter(pc -> pc.getCode().equals("ANOTHER"))
            .findFirst()
            .orElseThrow();
        assertThat(secondPromo.getTargetUsers()).hasSize(1);
    }

    @Test
    void fetchBagRelationships_ShouldMaintainOrderInList() {
        // Given
        PromoCode promo1 = new PromoCode();
        promo1.setCode("PROMO1");
        promo1.setDescription("Promo 1");
        promo1.setDescriptionEn("Promo 1");
        promo1.setIsActive(true);
        promo1.setTargetUsers(Set.of(testUser1));
        entityManager.persistAndFlush(promo1);

        PromoCode promo2 = new PromoCode();
        promo2.setCode("PROMO2");
        promo2.setDescription("Promo 2");
        promo2.setDescriptionEn("Promo 2");
        promo2.setIsActive(true);
        promo2.setTargetUsers(Set.of(testUser2));
        entityManager.persistAndFlush(promo2);

        entityManager.clear();
        List<PromoCode> orderedPromoCodes = List.of(promo2, promo1, testPromoCode);

        // When
        List<PromoCode> result = promoCodeRepository.fetchBagRelationships(orderedPromoCodes);

        // Then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).getCode()).isEqualTo("PROMO2");
        assertThat(result.get(1).getCode()).isEqualTo("PROMO1");
        assertThat(result.get(2).getCode()).isEqualTo("TESTPROMO");
    }

    @Test
    void fetchBagRelationships_ShouldHandlePromoCodeWithNoTargetUsers() {
        // Given
        PromoCode promoWithoutUsers = new PromoCode();
        promoWithoutUsers.setCode("NOUSERS");
        promoWithoutUsers.setDescription("No Users Promo");
        promoWithoutUsers.setDescriptionEn("No Users Promo");
        promoWithoutUsers.setIsActive(true);
        // No target users set
        entityManager.persistAndFlush(promoWithoutUsers);

        entityManager.clear();
        Optional<PromoCode> promoCodeOptional = promoCodeRepository.findById(promoWithoutUsers.getId());

        // When
        Optional<PromoCode> result = promoCodeRepository.fetchBagRelationships(promoCodeOptional);

        // Then
        assertThat(result).isPresent();
        PromoCode promoCode = result.get();
        assertThat(promoCode.getTargetUsers()).isEmpty();
    }

    @Test
    void findOneWithEagerRelationships_ShouldLoadTargetUsers() {
        // Given
        entityManager.clear(); // Clear persistence context

        // When
        Optional<PromoCode> result = promoCodeRepository.findOneWithEagerRelationships(testPromoCode.getId());

        // Then
        assertThat(result).isPresent();
        PromoCode promoCode = result.get();
        assertThat(promoCode.getCode()).isEqualTo("TESTPROMO");
        assertThat(promoCode.getTargetUsers()).hasSize(2);
        assertThat(promoCode.getTargetUsers()).extracting(User::getEmail)
            .containsExactlyInAnyOrder("<EMAIL>", "<EMAIL>");
    }

    @Test
    void findAllWithEagerRelationships_ShouldLoadTargetUsersForAllPromoCodes() {
        // Given
        PromoCode anotherPromoCode = new PromoCode();
        anotherPromoCode.setCode("EAGER");
        anotherPromoCode.setDescription("Eager Loading Test");
        anotherPromoCode.setDescriptionEn("Eager Loading Test");
        anotherPromoCode.setIsActive(true);
        anotherPromoCode.setTargetUsers(Set.of(testUser1));
        entityManager.persistAndFlush(anotherPromoCode);

        entityManager.clear();

        // When
        List<PromoCode> result = promoCodeRepository.findAllWithEagerRelationships();

        // Then
        assertThat(result).hasSizeGreaterThanOrEqualTo(2);
        
        PromoCode testPromo = result.stream()
            .filter(pc -> pc.getCode().equals("TESTPROMO"))
            .findFirst()
            .orElseThrow();
        assertThat(testPromo.getTargetUsers()).hasSize(2);

        PromoCode eagerPromo = result.stream()
            .filter(pc -> pc.getCode().equals("EAGER"))
            .findFirst()
            .orElseThrow();
        assertThat(eagerPromo.getTargetUsers()).hasSize(1);
    }

    @Test
    void findAllWithEagerRelationships_WithPageable_ShouldLoadTargetUsersForPagedPromoCodes() {
        // Given
        entityManager.clear();
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<PromoCode> result = promoCodeRepository.findAllWithEagerRelationships(pageable);

        // Then
        assertThat(result.getContent()).isNotEmpty();
        
        PromoCode promoCode = result.getContent().stream()
            .filter(pc -> pc.getCode().equals("TESTPROMO"))
            .findFirst()
            .orElseThrow();
        assertThat(promoCode.getTargetUsers()).hasSize(2);
    }
}
