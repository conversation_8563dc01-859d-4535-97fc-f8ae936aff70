package com.ferigobooking.api.repository;

import static org.assertj.core.api.Assertions.assertThat;

import com.ferigobooking.api.IntegrationTest;
import com.ferigobooking.api.domain.Authority;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.security.AuthoritiesConstants;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

@DataJpaTest
@ActiveProfiles("test")
class UserRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuthorityRepository authorityRepository;

    private User testUser;
    private Authority userAuthority;

    @BeforeEach
    void setUp() {
        // Create and persist authority
        userAuthority = new Authority();
        userAuthority.setName(AuthoritiesConstants.USER);
        entityManager.persistAndFlush(userAuthority);

        // Create test user
        testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setLangKey("en");
        testUser.setSupabaseUserId("supabase-123");
        testUser.setPassword("encoded-password");
        testUser.setAuthorities(Set.of(userAuthority));
        testUser.setCreatedDate(Instant.now());
        testUser.setLastModifiedDate(Instant.now());
    }

    @Test
    void findOneByEmailIgnoreCase_ShouldFindUserByEmail() {
        // Given
        entityManager.persistAndFlush(testUser);

        // When
        Optional<User> result = userRepository.findOneByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
        assertThat(result.get().getFirstName()).isEqualTo("Test");
        assertThat(result.get().getLastName()).isEqualTo("User");
    }

    @Test
    void findOneByEmailIgnoreCase_ShouldBeCaseInsensitive() {
        // Given
        entityManager.persistAndFlush(testUser);

        // When
        Optional<User> result = userRepository.findOneByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void findOneByEmailIgnoreCase_ShouldReturnEmptyWhenNotFound() {
        // When
        Optional<User> result = userRepository.findOneByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void findOneWithAuthoritiesByEmailIgnoreCase_ShouldFindUserWithAuthorities() {
        // Given
        entityManager.persistAndFlush(testUser);

        // When
        Optional<User> result = userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
        assertThat(result.get().getAuthorities()).hasSize(1);
        assertThat(result.get().getAuthorities().iterator().next().getName()).isEqualTo(AuthoritiesConstants.USER);
    }

    @Test
    void findOneWithAuthoritiesByEmailIgnoreCase_ShouldBeCaseInsensitive() {
        // Given
        entityManager.persistAndFlush(testUser);

        // When
        Optional<User> result = userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
        assertThat(result.get().getAuthorities()).hasSize(1);
    }

    @Test
    void findOneWithAuthoritiesByEmailIgnoreCase_ShouldReturnEmptyWhenNotFound() {
        // When
        Optional<User> result = userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void findAllByIdNotNull_ShouldReturnAllUsers() {
        // Given
        User anotherUser = new User();
        anotherUser.setEmail("<EMAIL>");
        anotherUser.setFirstName("Another");
        anotherUser.setLastName("User");
        anotherUser.setLangKey("en");
        anotherUser.setSupabaseUserId("supabase-456");
        anotherUser.setPassword("encoded-password");
        anotherUser.setAuthorities(Set.of(userAuthority));
        anotherUser.setCreatedDate(Instant.now());
        anotherUser.setLastModifiedDate(Instant.now());

        entityManager.persistAndFlush(testUser);
        entityManager.persistAndFlush(anotherUser);

        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<User> result = userRepository.findAllByIdNotNull(pageable);

        // Then
        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getContent()).extracting(User::getEmail)
            .containsExactlyInAnyOrder("<EMAIL>", "<EMAIL>");
    }

    @Test
    void findAllByIdNotNull_ShouldSupportPagination() {
        // Given
        for (int i = 0; i < 15; i++) {
            User user = new User();
            user.setEmail("user" + i + "@example.com");
            user.setFirstName("User" + i);
            user.setLastName("Test");
            user.setLangKey("en");
            user.setSupabaseUserId("supabase-" + i);
            user.setPassword("encoded-password");
            user.setAuthorities(Set.of(userAuthority));
            user.setCreatedDate(Instant.now());
            user.setLastModifiedDate(Instant.now());
            entityManager.persistAndFlush(user);
        }

        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<User> result = userRepository.findAllByIdNotNull(pageable);

        // Then
        assertThat(result.getContent()).hasSize(10);
        assertThat(result.getTotalElements()).isEqualTo(15);
        assertThat(result.getTotalPages()).isEqualTo(2);
        assertThat(result.hasNext()).isTrue();
    }

    @Test
    void findOneBySupabaseUserId_ShouldFindUserBySupabaseId() {
        // Given
        entityManager.persistAndFlush(testUser);

        // When
        Optional<User> result = userRepository.findOneBySupabaseUserId("supabase-123");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getSupabaseUserId()).isEqualTo("supabase-123");
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void findOneBySupabaseUserId_ShouldReturnEmptyWhenNotFound() {
        // When
        Optional<User> result = userRepository.findOneBySupabaseUserId("nonexistent-supabase-id");

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void findOneBySupabaseUserId_ShouldHandleNullSupabaseUserId() {
        // Given
        testUser.setSupabaseUserId(null);
        entityManager.persistAndFlush(testUser);

        // When
        Optional<User> result = userRepository.findOneBySupabaseUserId("supabase-123");

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void save_ShouldPersistUser() {
        // When
        User savedUser = userRepository.save(testUser);

        // Then
        assertThat(savedUser.getId()).isNotNull();
        assertThat(savedUser.getEmail()).isEqualTo("<EMAIL>");
        assertThat(savedUser.getCreatedDate()).isNotNull();
        assertThat(savedUser.getLastModifiedDate()).isNotNull();
    }

    @Test
    void save_ShouldUpdateExistingUser() {
        // Given
        entityManager.persistAndFlush(testUser);
        Long userId = testUser.getId();

        // When
        testUser.setFirstName("Updated");
        testUser.setLastName("Name");
        User updatedUser = userRepository.save(testUser);

        // Then
        assertThat(updatedUser.getId()).isEqualTo(userId);
        assertThat(updatedUser.getFirstName()).isEqualTo("Updated");
        assertThat(updatedUser.getLastName()).isEqualTo("Name");
        assertThat(updatedUser.getLastModifiedDate()).isAfter(updatedUser.getCreatedDate());
    }

    @Test
    void delete_ShouldRemoveUser() {
        // Given
        entityManager.persistAndFlush(testUser);
        Long userId = testUser.getId();

        // When
        userRepository.delete(testUser);
        entityManager.flush();

        // Then
        Optional<User> result = userRepository.findById(userId);
        assertThat(result).isEmpty();
    }

    @Test
    void findOneWithAuthoritiesByEmailIgnoreCase_ShouldEagerLoadAuthorities() {
        // Given
        Authority adminAuthority = new Authority();
        adminAuthority.setName(AuthoritiesConstants.ADMIN);
        entityManager.persistAndFlush(adminAuthority);

        testUser.getAuthorities().add(adminAuthority);
        entityManager.persistAndFlush(testUser);
        entityManager.clear(); // Clear persistence context to ensure lazy loading

        // When
        Optional<User> result = userRepository.findOneWithAuthoritiesByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isPresent();
        User user = result.get();
        // Authorities should be loaded eagerly due to @EntityGraph
        assertThat(user.getAuthorities()).hasSize(2);
        assertThat(user.getAuthorities()).extracting(Authority::getName)
            .containsExactlyInAnyOrder(AuthoritiesConstants.USER, AuthoritiesConstants.ADMIN);
    }

    @Test
    void findOneByEmailIgnoreCase_ShouldHandleSpecialCharactersInEmail() {
        // Given
        testUser.setEmail("<EMAIL>");
        entityManager.persistAndFlush(testUser);

        // When
        Optional<User> result = userRepository.findOneByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
    }
}
