package com.ferigobooking.api;

import com.ferigobooking.api.config.AsyncSyncConfiguration;
import com.ferigobooking.api.config.EmbeddedSQL;
import com.ferigobooking.api.config.JacksonConfiguration;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

/**
 * Base composite annotation for integration tests.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest(
    classes = { FerigoBackend2025App.class, JacksonConfiguration.class, AsyncSyncConfiguration.class }
)
@EmbeddedSQL
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public @interface IntegrationTest {
}
