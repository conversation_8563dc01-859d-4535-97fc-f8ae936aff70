<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>

<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <logger name="com.ferigobooking.api" level="INFO"/>

    <logger name="tech.jhipster" level="WARN"/>

    <!-- https://www.testcontainers.org/supported_docker_environment/logging_config/ -->
    <logger name="org.testcontainers" level="INFO"/>
    <logger name="com.github.dockerjava" level="WARN"/>

    <logger name="jakarta.activation" level="WARN"/>
    <logger name="jakarta.mail" level="WARN"/>
    <logger name="jakarta.xml.bind" level="WARN"/>
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="com.jayway.jsonpath" level="WARN"/>
    <logger name="com.ryantenney" level="WARN"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="io.undertow" level="WARN"/>
    <logger name="io.undertow.websockets.jsr" level="ERROR"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
    <logger name="org.bson" level="WARN"/>
    <logger name="org.hibernate.validator" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.hibernate.ejb.HibernatePersistence" level="OFF"/>
    <logger name="org.postgresql.jdbc" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.cache" level="WARN"/>
    <logger name="org.thymeleaf" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="io.swagger.v3" level="INFO"/>
    <logger name="sun.rmi" level="WARN"/>
    <logger name="sun.rmi.transport" level="WARN"/>
    <logger name="com.tngtech.archunit.core.importer" level="ERROR"/>
    <logger name="org.hibernate.orm.incubating" level="ERROR"/>
    <logger name="liquibase" level="WARN"/>
    <logger name="LiquibaseSchemaResolver" level="INFO"/>
    <!-- jhipster-needle-logback-add-log - JHipster will add a new log with level -->

</configuration>
