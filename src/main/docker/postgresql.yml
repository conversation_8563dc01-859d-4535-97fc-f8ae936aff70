# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: ferigobackend2025
services:
  postgresql:
    image: postgres:17.4
    # volumes:
    #   - ~/volumes/jhipster/ferigoBackend2025/postgresql/:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=ferigoBackend2025
      - POSTGRES_HOST_AUTH_METHOD=trust
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER}']
      interval: 5s
      timeout: 5s
      retries: 10
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:5432:5432
