# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: INFO
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.ferigobooking.api: DEBUG

spring:
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ********************************************
    username: postgres
    password: 123456
    hikari:
      poolName: Hikari
      auto-commit: false
  liquibase:
    contexts: dev
  mail:
    host: localhost
    port: 25
    username:
    password:
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false

server:
  port: 8080
  # make sure requests the proxy uri instead of the server one
  forward-headers-strategy: native

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  cache: # Cache configuration
    redis: # Redis configuration
      expiration: 3600 # By default objects stay 1 hour (in seconds) in the cache
      server: redis://localhost:6379
      cluster: false
      # server: redis://localhost:6379,redis://localhost:16379,redis://localhost:26379
      # cluster: true
  # CORS is only enabled by default with the "dev" profile
  cors:
    # Allow Ionic for JHipster by default (* no longer allowed in Spring Boot 2.4+)
    allowed-origins: 'http://localhost:8100,https://localhost:8100'
    # Enable CORS when running in GitHub Codespaces
    allowed-origin-patterns: 'https://*.githubpreview.dev'
    allowed-methods: '*'
    allowed-headers: '*'
    exposed-headers: 'Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params'
    allow-credentials: true
    max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: MzgzNjk4NDY3ZWQyMzI1OGRkYzA0Yzc1ZjdlYTU0ZTM3NTAxOGI4YTYxZTgzNzViOGY3NmY2MzJmOTY2Y2FkNmQ4MzRjZmRhZDVlNzdiYjJkNDA2ZmVkMzdmMWIwM2NmMTE0Yjc4YmQ1YmJjZTk5NGU0YWRhYzQ2OWE1OTIyMjQ=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  mail: # specific JHipster mail property, for standard properties see MailProperties
    base-url: http://127.0.0.1:8080
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# Supabase configuration
supabase:
  url: ${SUPABASE_URL:https://gbroexkwqujjtafobgod.supabase.co}
  anon-key: ${SUPABASE_ANON_KEY:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdicm9leGt3cXVqanRhZm9iZ29kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjU4NTMwMDYsImV4cCI6MjA0MTQyOTAwNn0.OfIp3m2CX0yk83KAjVBU7UWMyKjkNBY6m3RiYzWSnPQ}
  service-role-key: ${SUPABASE_SERVICE_ROLE_KEY:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdicm9leGt3cXVqanRhZm9iZ29kIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTg1MzAwNiwiZXhwIjoyMDQxNDI5MDA2fQ.KpFYgKMw-I5k7GJrSI1ZtEE5NQSPFe6nYTnlq1chjRM}
  jwt-secret: ${SUPABASE_JWT_SECRET:jn7Otiw4tpEk30kh6NQLFinQB8v0ih5bVtVBOjWOBGdc4zaA5aOQ2VV6sP+TWg/B7wwfn0IUFUUySw/c/EwTRA==}
# application:
