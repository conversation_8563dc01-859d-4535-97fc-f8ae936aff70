<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <property name="now" value="current_timestamp" dbms="postgresql"/>
    <property name="floatType" value="float4" dbms="postgresql"/>
    <property name="clobType" value="clob" dbms="postgresql"/>
    <property name="blobType" value="blob" dbms="postgresql"/>
    <property name="uuidType" value="uuid" dbms="postgresql"/>
    <property name="datetimeType" value="datetime" dbms="postgresql"/>
    <property name="timeType" value="time(6)" dbms="postgresql"/>

    <include file="config/liquibase/changelog/20250705-203938-init.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250706-185137-changelog.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
    <!-- jhipster-needle-liquibase-add-incremental-changelog - JHipster will add incremental liquibase changelogs here -->
</databaseChangeLog>
