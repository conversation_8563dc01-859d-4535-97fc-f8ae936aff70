<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="*************-1" author="rejam">
        <createSequence incrementBy="1" sequenceName="ancillary_product_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-2" author="rejam">
        <createSequence incrementBy="1" sequenceName="base_price_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-3" author="rejam">
        <createSequence incrementBy="1" sequenceName="booking_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-4" author="rejam">
        <createSequence incrementBy="1" sequenceName="city_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-5" author="rejam">
        <createSequence incrementBy="1" sequenceName="common_lookup_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-6" author="rejam">
        <createSequence incrementBy="1" sequenceName="country_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-7" author="rejam">
        <createSequence incrementBy="1" sequenceName="ferry_port_lookup_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-8" author="rejam">
        <createSequence incrementBy="1" sequenceName="ferry_port_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-9" author="rejam">
        <createSequence incrementBy="1" sequenceName="jhi_order_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-10" author="rejam">
        <createSequence incrementBy="1" sequenceName="jhi_user_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-11" author="rejam">
        <createSequence incrementBy="1" sequenceName="loyalty_program_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-12" author="rejam">
        <createSequence incrementBy="1" sequenceName="loyalty_redemption_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-13" author="rejam">
        <createSequence incrementBy="1" sequenceName="loyalty_reward_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-14" author="rejam">
        <createSequence incrementBy="1" sequenceName="loyalty_rule_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-15" author="rejam">
        <createSequence incrementBy="1" sequenceName="loyalty_tier_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-16" author="rejam">
        <createSequence incrementBy="1" sequenceName="loyalty_transaction_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-17" author="rejam">
        <createSequence incrementBy="1" sequenceName="news_category_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-18" author="rejam">
        <createSequence incrementBy="1" sequenceName="news_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-19" author="rejam">
        <createSequence incrementBy="1" sequenceName="notification_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-20" author="rejam">
        <createSequence incrementBy="1" sequenceName="notification_template_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-21" author="rejam">
        <createSequence incrementBy="1" sequenceName="operator_route_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-22" author="rejam">
        <createSequence incrementBy="1" sequenceName="operator_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-23" author="rejam">
        <createSequence incrementBy="1" sequenceName="order_item_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-24" author="rejam">
        <createSequence incrementBy="1" sequenceName="organiser_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-25" author="rejam">
        <createSequence incrementBy="1" sequenceName="payment_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-26" author="rejam">
        <createSequence incrementBy="1" sequenceName="port_sector_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-27" author="rejam">
        <createSequence incrementBy="1" sequenceName="price_adjustment_rule_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-28" author="rejam">
        <createSequence incrementBy="1" sequenceName="pricing_component_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-29" author="rejam">
        <createSequence incrementBy="1" sequenceName="promo_code_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-30" author="rejam">
        <createSequence incrementBy="1" sequenceName="promo_code_tracking_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-31" author="rejam">
        <createSequence incrementBy="1" sequenceName="route_lookup_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-32" author="rejam">
        <createSequence incrementBy="1" sequenceName="route_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-33" author="rejam">
        <createSequence incrementBy="1" sequenceName="travel_passenger_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-34" author="rejam">
        <createSequence incrementBy="1" sequenceName="trip_plan_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-35" author="rejam">
        <createSequence incrementBy="1" sequenceName="trip_schedule_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-36" author="rejam">
        <createSequence incrementBy="1" sequenceName="user_loyalty_account_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-37" author="rejam">
        <createSequence incrementBy="1" sequenceName="user_passenger_seq" startValue="1"/>
    </changeSet>
    <changeSet id="*************-38" author="rejam">
        <createTable tableName="ancillary_product">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_ancillary_product"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="description_en" type="VARCHAR(255)"/>
            <column name="price" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="product_category" type="VARCHAR(255)"/>
            <column name="enabled" type="BOOLEAN"/>
            <column name="operator_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-39" author="rejam">
        <createTable tableName="base_price">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_base_price"/>
            </column>
            <column name="base_price" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="passenger_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="direction_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="nationality" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="route_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-40" author="rejam">
        <createTable tableName="booking">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_booking"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="organiser_fullname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="organiser_email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="organiser_phone" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_round_trip" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="is_open_return" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="op_confirmation_code" type="VARCHAR(255)"/>
            <column name="order_id" type="BIGINT"/>
            <column name="operator_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="organiser_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-41" author="rejam">
        <createTable tableName="city">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_city"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(6)">
                <constraints nullable="false"/>
            </column>
            <column name="country_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-42" author="rejam">
        <createTable tableName="common_lookup">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_common_lookup"/>
            </column>
            <column name="key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-43" author="rejam">
        <createTable tableName="country">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_country"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-44" author="rejam">
        <createTable tableName="ferry_port">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_ferry_port"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="city_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-45" author="rejam">
        <createTable tableName="ferry_port_lookup">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_ferry_port_lookup"/>
            </column>
            <column name="ref_code" type="VARCHAR(255)"/>
            <column name="operator_id" type="BIGINT"/>
            <column name="ferry_port_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-46" author="rejam">
        <createTable tableName="jhi_authority">
            <column name="name" type="VARCHAR(50)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_jhi_authority"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-47" author="rejam">
        <createTable tableName="jhi_order">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_jhi_order"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="total_price" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="total_discount" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="charged_amount" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="total_fee" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="organiser_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="organiser_phone" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="organiser_email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="notes" type="VARCHAR(255)"/>
            <column name="customer_user_ref_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-48" author="rejam">
        <createTable tableName="jhi_user">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_jhi_user"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="password_hash" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column name="first_name" type="VARCHAR(50)"/>
            <column name="last_name" type="VARCHAR(50)"/>
            <column name="email" type="VARCHAR(254)">
                <constraints nullable="false"/>
            </column>
            <column name="lang_key" type="VARCHAR(10)"/>
            <column name="image_url" type="VARCHAR(256)"/>
            <column name="supabase_user_id" type="VARCHAR(255)"/>
            <column name="oauth_provider" type="VARCHAR(255)"/>
            <column name="oauth_provider_id" type="VARCHAR(255)"/>
            <column name="avatar_url" type="VARCHAR(255)"/>
            <column name="email_verified" type="BOOLEAN"/>
            <column name="phone_verified" type="BOOLEAN"/>
            <column name="phone_number" type="VARCHAR(255)"/>
            <column name="provider" type="VARCHAR(255)"/>
            <column name="gender" type="VARCHAR(255)"/>
            <column name="dob" type="DATE"/>
            <column name="nationality_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-49" author="rejam">
        <createTable tableName="jhi_user_authority">
            <column name="authority_name" type="VARCHAR(50)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_jhi_user_authority"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_jhi_user_authority"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-50" author="rejam">
        <createTable tableName="loyalty_program">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loyalty_program"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="enabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="earn_rule_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-51" author="rejam">
        <createTable tableName="loyalty_redemption">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loyalty_redemption"/>
            </column>
            <column name="redeemed_at" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column name="reward_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-52" author="rejam">
        <createTable tableName="loyalty_reward">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loyalty_reward"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="points_required" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="reward_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="quantity" type="INT"/>
            <column name="expiry_date" type="DATETIME"/>
            <column name="is_active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-53" author="rejam">
        <createTable tableName="loyalty_rule">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loyalty_rule"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="rule_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="json_rule_definition" type="oid">
                <constraints nullable="false"/>
            </column>
            <column name="enabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-54" author="rejam">
        <createTable tableName="loyalty_tier">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loyalty_tier"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="min_points" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="benefits" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-55" author="rejam">
        <createTable tableName="loyalty_transaction">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loyalty_transaction"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="points" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="reason" type="VARCHAR(255)"/>
            <column name="reference_id" type="VARCHAR(255)"/>
            <column name="expired_at" type="DATETIME"/>
            <column name="user_id" type="BIGINT"/>
            <column name="program_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-56" author="rejam">
        <createTable tableName="news">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_news"/>
            </column>
            <column name="title" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="title_en" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="content" type="oid">
                <constraints nullable="false"/>
            </column>
            <column name="content_en" type="oid">
                <constraints nullable="false"/>
            </column>
            <column name="caption" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="caption_en" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="banner_url" type="VARCHAR(255)"/>
            <column name="published" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="category_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-57" author="rejam">
        <createTable tableName="news_category">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_news_category"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-58" author="rejam">
        <createTable tableName="notification">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_notification"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="title" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="oid">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="channel" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="target_url" type="VARCHAR(255)"/>
            <column name="is_read" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="sent_at" type="DATETIME"/>
            <column name="read_at" type="DATETIME"/>
            <column name="recipient_id" type="BIGINT"/>
            <column name="template_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-59" author="rejam">
        <createTable tableName="notification_template">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_notification_template"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="trigger_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="trigger_event" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="scheduled_time" type="DATETIME"/>
            <column name="recurring_cron_expression" type="VARCHAR(255)"/>
            <column name="channel" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="title_template" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="body_template" type="oid">
                <constraints nullable="false"/>
            </column>
            <column name="target_url_template" type="VARCHAR(255)"/>
            <column name="enabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-60" author="rejam">
        <createTable tableName="operator">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_operator"/>
            </column>
            <column name="logo_url" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="enabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="instant_confirmation" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="open_ticket_expired_at" type="DATE"/>
            <column name="open_ticket_expiry_duration_in_months" type="INT"/>
            <column name="open_ticket_validity_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-61" author="rejam">
        <createTable tableName="operator_route">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_operator_route"/>
            </column>
            <column name="allow_open_return" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="allow_return" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="enabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" type="BIGINT"/>
            <column name="route_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-62" author="rejam">
        <createTable tableName="order_item">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_order_item"/>
            </column>
            <column name="quantity" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="price" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name_en" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="product_ref_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="order_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-63" author="rejam">
        <createTable tableName="organiser">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_organiser"/>
            </column>
            <column name="full_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phone" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-64" author="rejam">
        <createTable tableName="payment">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_payment"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="amount_paid" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="payment_method" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="va_number" type="VARCHAR(255)"/>
            <column name="paid_at" type="DATETIME"/>
            <column name="cancelled_at" type="DATETIME"/>
            <column name="expired_at" type="DATETIME"/>
            <column name="gateway_reference_id" type="VARCHAR(255)"/>
            <column name="order_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-65" author="rejam">
        <createTable tableName="port_sector">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_port_sector"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="next_sector_code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-66" author="rejam">
        <createTable tableName="price_adjustment_rule">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_price_adjustment_rule"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name_en" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="adjustment_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="target_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="json_rule_definition" type="oid">
                <constraints nullable="false"/>
            </column>
            <column name="enabled" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-67" author="rejam">
        <createTable tableName="pricing_component">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_pricing_component"/>
            </column>
            <column name="description" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description_en" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="applied_rule" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="order_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-68" author="rejam">
        <createTable tableName="promo_code">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_promo_code"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="description_en" type="VARCHAR(255)"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="rule_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-69" author="rejam">
        <createTable tableName="promo_code_tracking">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_promo_code_tracking"/>
            </column>
            <column name="redeemed_at" type="DATETIME"/>
            <column name="order_id" type="BIGINT"/>
            <column name="code_id" type="BIGINT"/>
            <column name="redeemed_by_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-70" author="rejam">
        <createTable tableName="rel_promo_code__target_users">
            <column name="promo_code_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_rel_promo_code__target_users"/>
            </column>
            <column name="target_users_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_rel_promo_code__target_users"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-71" author="rejam">
        <createTable tableName="route">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_route"/>
            </column>
            <column name="code" type="VARCHAR(6)">
                <constraints nullable="false"/>
            </column>
            <column name="start_port_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="end_port_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="sector_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-72" author="rejam">
        <createTable tableName="route_lookup">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_route_lookup"/>
            </column>
            <column name="ref_code" type="VARCHAR(255)"/>
            <column name="operator_id" type="BIGINT"/>
            <column name="route_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-73" author="rejam">
        <createTable tableName="travel_passenger">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_travel_passenger"/>
            </column>
            <column name="full_name" type="VARCHAR(255)"/>
            <column name="gender" type="VARCHAR(255)"/>
            <column name="dob" type="DATE"/>
            <column name="passport_no" type="VARCHAR(255)"/>
            <column name="passport_expiry_date" type="DATE"/>
            <column name="type" type="VARCHAR(255)"/>
            <column name="nationality_id" type="BIGINT"/>
            <column name="passport_issuance_country_id" type="BIGINT"/>
            <column name="master_passenger_id" type="BIGINT"/>
            <column name="trip_plan_id" type="BIGINT"/>
            <column name="fare_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-74" author="rejam">
        <createTable tableName="trip_plan">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_trip_plan"/>
            </column>
            <column name="code" type="VARCHAR(255)"/>
            <column name="departure_time" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="next_trip_code" type="VARCHAR(255)"/>
            <column name="previous_trip_code" type="VARCHAR(255)"/>
            <column name="duration_in_minutes" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="is_open_trip" type="BOOLEAN"/>
            <column name="open_ticket_expired_at" type="DATE"/>
            <column name="route_id" type="BIGINT"/>
            <column name="booking_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-75" author="rejam">
        <createTable tableName="trip_schedule">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_trip_schedule"/>
            </column>
            <column name="day" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="depart_time" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="duration_in_minutes" type="INT"/>
            <column name="enabled" type="BOOLEAN"/>
            <column name="operator_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="route_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="*************-76" author="rejam">
        <createTable tableName="user_loyalty_account">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_user_loyalty_account"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="DATETIME"/>
            <column name="total_points" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="available_points" type="DECIMAL(21, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column name="tier_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-77" author="rejam">
        <createTable tableName="user_passenger">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_user_passenger"/>
            </column>
            <column name="full_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="gender" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="dob" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="passport_no" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="passport_expiry_date" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="nationality_id" type="BIGINT"/>
            <column name="passport_issuance_country_id" type="BIGINT"/>
            <column name="user_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-78" author="rejam">
        <addUniqueConstraint columnNames="order_id" constraintName="uc_booking_order" tableName="booking"/>
    </changeSet>
    <changeSet id="*************-79" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_city_code" tableName="city"/>
    </changeSet>
    <changeSet id="*************-80" author="rejam">
        <addUniqueConstraint columnNames="name" constraintName="uc_city_name" tableName="city"/>
    </changeSet>
    <changeSet id="*************-81" author="rejam">
        <addUniqueConstraint columnNames="key" constraintName="uc_common_lookup_key" tableName="common_lookup"/>
    </changeSet>
    <changeSet id="*************-82" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_country_code" tableName="country"/>
    </changeSet>
    <changeSet id="*************-83" author="rejam">
        <addUniqueConstraint columnNames="name" constraintName="uc_country_name" tableName="country"/>
    </changeSet>
    <changeSet id="*************-84" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_ferry_port_code" tableName="ferry_port"/>
    </changeSet>
    <changeSet id="*************-85" author="rejam">
        <addUniqueConstraint columnNames="name" constraintName="uc_ferry_port_name" tableName="ferry_port"/>
    </changeSet>
    <changeSet id="*************-86" author="rejam">
        <addUniqueConstraint columnNames="email" constraintName="uc_jhi_user_email" tableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-87" author="rejam">
        <addUniqueConstraint columnNames="supabase_user_id" constraintName="uc_jhi_user_supabase_user"
                             tableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-88" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_loyalty_reward_code" tableName="loyalty_reward"/>
    </changeSet>
    <changeSet id="*************-89" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_loyalty_rule_code" tableName="loyalty_rule"/>
    </changeSet>
    <changeSet id="*************-90" author="rejam">
        <addUniqueConstraint columnNames="name" constraintName="uc_loyalty_tier_name" tableName="loyalty_tier"/>
    </changeSet>
    <changeSet id="*************-91" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_notification_template_code"
                             tableName="notification_template"/>
    </changeSet>
    <changeSet id="*************-92" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_operator_code" tableName="operator"/>
    </changeSet>
    <changeSet id="*************-93" author="rejam">
        <addUniqueConstraint columnNames="name" constraintName="uc_operator_name" tableName="operator"/>
    </changeSet>
    <changeSet id="*************-94" author="rejam">
        <addUniqueConstraint columnNames="order_id" constraintName="uc_payment_order" tableName="payment"/>
    </changeSet>
    <changeSet id="*************-95" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_promo_code_code" tableName="promo_code"/>
    </changeSet>
    <changeSet id="*************-96" author="rejam">
        <addUniqueConstraint columnNames="code" constraintName="uc_route_code" tableName="route"/>
    </changeSet>
    <changeSet id="*************-97" author="rejam">
        <addForeignKeyConstraint baseColumnNames="operator_id" baseTableName="ancillary_product"
                                 constraintName="FK_ANCILLARY_PRODUCT_ON_OPERATOR" referencedColumnNames="id"
                                 referencedTableName="operator"/>
    </changeSet>
    <changeSet id="*************-98" author="rejam">
        <addForeignKeyConstraint baseColumnNames="operator_id" baseTableName="base_price"
                                 constraintName="FK_BASE_PRICE_ON_OPERATOR" referencedColumnNames="id"
                                 referencedTableName="operator"/>
    </changeSet>
    <changeSet id="*************-99" author="rejam">
        <addForeignKeyConstraint baseColumnNames="route_id" baseTableName="base_price"
                                 constraintName="FK_BASE_PRICE_ON_ROUTE" referencedColumnNames="id"
                                 referencedTableName="route"/>
    </changeSet>
    <changeSet id="*************-100" author="rejam">
        <addForeignKeyConstraint baseColumnNames="operator_id" baseTableName="booking"
                                 constraintName="FK_BOOKING_ON_OPERATOR" referencedColumnNames="id"
                                 referencedTableName="operator"/>
    </changeSet>
    <changeSet id="*************-101" author="rejam">
        <addForeignKeyConstraint baseColumnNames="order_id" baseTableName="booking" constraintName="FK_BOOKING_ON_ORDER"
                                 referencedColumnNames="id" referencedTableName="jhi_order"/>
    </changeSet>
    <changeSet id="*************-102" author="rejam">
        <addForeignKeyConstraint baseColumnNames="organiser_id" baseTableName="booking"
                                 constraintName="FK_BOOKING_ON_ORGANISER" referencedColumnNames="id"
                                 referencedTableName="organiser"/>
    </changeSet>
    <changeSet id="*************-103" author="rejam">
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="city" constraintName="FK_CITY_ON_COUNTRY"
                                 referencedColumnNames="id" referencedTableName="country"/>
    </changeSet>
    <changeSet id="*************-104" author="rejam">
        <addForeignKeyConstraint baseColumnNames="ferry_port_id" baseTableName="ferry_port_lookup"
                                 constraintName="FK_FERRY_PORT_LOOKUP_ON_FERRYPORT" referencedColumnNames="id"
                                 referencedTableName="ferry_port"/>
    </changeSet>
    <changeSet id="*************-105" author="rejam">
        <addForeignKeyConstraint baseColumnNames="operator_id" baseTableName="ferry_port_lookup"
                                 constraintName="FK_FERRY_PORT_LOOKUP_ON_OPERATOR" referencedColumnNames="id"
                                 referencedTableName="operator"/>
    </changeSet>
    <changeSet id="*************-106" author="rejam">
        <addForeignKeyConstraint baseColumnNames="city_id" baseTableName="ferry_port"
                                 constraintName="FK_FERRY_PORT_ON_CITY" referencedColumnNames="id"
                                 referencedTableName="city"/>
    </changeSet>
    <changeSet id="*************-107" author="rejam">
        <addForeignKeyConstraint baseColumnNames="customer_user_ref_id" baseTableName="jhi_order"
                                 constraintName="FK_JHI_ORDER_ON_CUSTOMERUSERREF" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-108" author="rejam">
        <addForeignKeyConstraint baseColumnNames="nationality_id" baseTableName="jhi_user"
                                 constraintName="FK_JHI_USER_ON_NATIONALITY" referencedColumnNames="id"
                                 referencedTableName="country"/>
    </changeSet>
    <changeSet id="*************-109" author="rejam">
        <addForeignKeyConstraint baseColumnNames="earn_rule_id" baseTableName="loyalty_program"
                                 constraintName="FK_LOYALTY_PROGRAM_ON_EARNRULE" referencedColumnNames="id"
                                 referencedTableName="loyalty_rule"/>
    </changeSet>
    <changeSet id="*************-110" author="rejam">
        <addForeignKeyConstraint baseColumnNames="reward_id" baseTableName="loyalty_redemption"
                                 constraintName="FK_LOYALTY_REDEMPTION_ON_REWARD" referencedColumnNames="id"
                                 referencedTableName="loyalty_reward"/>
    </changeSet>
    <changeSet id="*************-111" author="rejam">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="loyalty_redemption"
                                 constraintName="FK_LOYALTY_REDEMPTION_ON_USER" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-112" author="rejam">
        <addForeignKeyConstraint baseColumnNames="program_id" baseTableName="loyalty_transaction"
                                 constraintName="FK_LOYALTY_TRANSACTION_ON_PROGRAM" referencedColumnNames="id"
                                 referencedTableName="loyalty_program"/>
    </changeSet>
    <changeSet id="*************-113" author="rejam">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="loyalty_transaction"
                                 constraintName="FK_LOYALTY_TRANSACTION_ON_USER" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-114" author="rejam">
        <addForeignKeyConstraint baseColumnNames="category_id" baseTableName="news" constraintName="FK_NEWS_ON_CATEGORY"
                                 referencedColumnNames="id" referencedTableName="news_category"/>
    </changeSet>
    <changeSet id="*************-115" author="rejam">
        <addForeignKeyConstraint baseColumnNames="recipient_id" baseTableName="notification"
                                 constraintName="FK_NOTIFICATION_ON_RECIPIENT" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-116" author="rejam">
        <addForeignKeyConstraint baseColumnNames="template_id" baseTableName="notification"
                                 constraintName="FK_NOTIFICATION_ON_TEMPLATE" referencedColumnNames="id"
                                 referencedTableName="notification_template"/>
    </changeSet>
    <changeSet id="*************-117" author="rejam">
        <addForeignKeyConstraint baseColumnNames="operator_id" baseTableName="operator_route"
                                 constraintName="FK_OPERATOR_ROUTE_ON_OPERATOR" referencedColumnNames="id"
                                 referencedTableName="operator"/>
    </changeSet>
    <changeSet id="*************-118" author="rejam">
        <addForeignKeyConstraint baseColumnNames="route_id" baseTableName="operator_route"
                                 constraintName="FK_OPERATOR_ROUTE_ON_ROUTE" referencedColumnNames="id"
                                 referencedTableName="route"/>
    </changeSet>
    <changeSet id="*************-119" author="rejam">
        <addForeignKeyConstraint baseColumnNames="order_id" baseTableName="order_item"
                                 constraintName="FK_ORDER_ITEM_ON_ORDER" referencedColumnNames="id"
                                 referencedTableName="jhi_order"/>
    </changeSet>
    <changeSet id="*************-120" author="rejam">
        <addForeignKeyConstraint baseColumnNames="order_id" baseTableName="payment" constraintName="FK_PAYMENT_ON_ORDER"
                                 referencedColumnNames="id" referencedTableName="jhi_order"/>
    </changeSet>
    <changeSet id="*************-121" author="rejam">
        <addForeignKeyConstraint baseColumnNames="order_id" baseTableName="pricing_component"
                                 constraintName="FK_PRICING_COMPONENT_ON_ORDER" referencedColumnNames="id"
                                 referencedTableName="jhi_order"/>
    </changeSet>
    <changeSet id="*************-122" author="rejam">
        <addForeignKeyConstraint baseColumnNames="rule_id" baseTableName="promo_code"
                                 constraintName="FK_PROMO_CODE_ON_RULE" referencedColumnNames="id"
                                 referencedTableName="price_adjustment_rule"/>
    </changeSet>
    <changeSet id="*************-123" author="rejam">
        <addForeignKeyConstraint baseColumnNames="code_id" baseTableName="promo_code_tracking"
                                 constraintName="FK_PROMO_CODE_TRACKING_ON_CODE" referencedColumnNames="id"
                                 referencedTableName="promo_code"/>
    </changeSet>
    <changeSet id="*************-124" author="rejam">
        <addForeignKeyConstraint baseColumnNames="order_id" baseTableName="promo_code_tracking"
                                 constraintName="FK_PROMO_CODE_TRACKING_ON_ORDER" referencedColumnNames="id"
                                 referencedTableName="jhi_order"/>
    </changeSet>
    <changeSet id="*************-125" author="rejam">
        <addForeignKeyConstraint baseColumnNames="redeemed_by_id" baseTableName="promo_code_tracking"
                                 constraintName="FK_PROMO_CODE_TRACKING_ON_REDEEMEDBY" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-126" author="rejam">
        <addForeignKeyConstraint baseColumnNames="operator_id" baseTableName="route_lookup"
                                 constraintName="FK_ROUTE_LOOKUP_ON_OPERATOR" referencedColumnNames="id"
                                 referencedTableName="operator"/>
    </changeSet>
    <changeSet id="*************-127" author="rejam">
        <addForeignKeyConstraint baseColumnNames="route_id" baseTableName="route_lookup"
                                 constraintName="FK_ROUTE_LOOKUP_ON_ROUTE" referencedColumnNames="id"
                                 referencedTableName="route"/>
    </changeSet>
    <changeSet id="*************-128" author="rejam">
        <addForeignKeyConstraint baseColumnNames="end_port_id" baseTableName="route"
                                 constraintName="FK_ROUTE_ON_ENDPORT" referencedColumnNames="id"
                                 referencedTableName="ferry_port"/>
    </changeSet>
    <changeSet id="*************-129" author="rejam">
        <addForeignKeyConstraint baseColumnNames="sector_id" baseTableName="route" constraintName="FK_ROUTE_ON_SECTOR"
                                 referencedColumnNames="id" referencedTableName="port_sector"/>
    </changeSet>
    <changeSet id="*************-130" author="rejam">
        <addForeignKeyConstraint baseColumnNames="start_port_id" baseTableName="route"
                                 constraintName="FK_ROUTE_ON_STARTPORT" referencedColumnNames="id"
                                 referencedTableName="ferry_port"/>
    </changeSet>
    <changeSet id="*************-131" author="rejam">
        <addForeignKeyConstraint baseColumnNames="fare_id" baseTableName="travel_passenger"
                                 constraintName="FK_TRAVEL_PASSENGER_ON_FARE" referencedColumnNames="id"
                                 referencedTableName="base_price"/>
    </changeSet>
    <changeSet id="*************-132" author="rejam">
        <addForeignKeyConstraint baseColumnNames="master_passenger_id" baseTableName="travel_passenger"
                                 constraintName="FK_TRAVEL_PASSENGER_ON_MASTERPASSENGER" referencedColumnNames="id"
                                 referencedTableName="user_passenger"/>
    </changeSet>
    <changeSet id="*************-133" author="rejam">
        <addForeignKeyConstraint baseColumnNames="nationality_id" baseTableName="travel_passenger"
                                 constraintName="FK_TRAVEL_PASSENGER_ON_NATIONALITY" referencedColumnNames="id"
                                 referencedTableName="country"/>
    </changeSet>
    <changeSet id="*************-134" author="rejam">
        <addForeignKeyConstraint baseColumnNames="passport_issuance_country_id" baseTableName="travel_passenger"
                                 constraintName="FK_TRAVEL_PASSENGER_ON_PASSPORTISSUANCECOUNTRY"
                                 referencedColumnNames="id" referencedTableName="country"/>
    </changeSet>
    <changeSet id="*************-135" author="rejam">
        <addForeignKeyConstraint baseColumnNames="trip_plan_id" baseTableName="travel_passenger"
                                 constraintName="FK_TRAVEL_PASSENGER_ON_TRIPPLAN" referencedColumnNames="id"
                                 referencedTableName="trip_plan"/>
    </changeSet>
    <changeSet id="*************-136" author="rejam">
        <addForeignKeyConstraint baseColumnNames="booking_id" baseTableName="trip_plan"
                                 constraintName="FK_TRIP_PLAN_ON_BOOKING" referencedColumnNames="id"
                                 referencedTableName="booking"/>
    </changeSet>
    <changeSet id="*************-137" author="rejam">
        <addForeignKeyConstraint baseColumnNames="route_id" baseTableName="trip_plan"
                                 constraintName="FK_TRIP_PLAN_ON_ROUTE" referencedColumnNames="id"
                                 referencedTableName="route"/>
    </changeSet>
    <changeSet id="*************-138" author="rejam">
        <addForeignKeyConstraint baseColumnNames="operator_id" baseTableName="trip_schedule"
                                 constraintName="FK_TRIP_SCHEDULE_ON_OPERATOR" referencedColumnNames="id"
                                 referencedTableName="operator"/>
    </changeSet>
    <changeSet id="*************-139" author="rejam">
        <addForeignKeyConstraint baseColumnNames="route_id" baseTableName="trip_schedule"
                                 constraintName="FK_TRIP_SCHEDULE_ON_ROUTE" referencedColumnNames="id"
                                 referencedTableName="route"/>
    </changeSet>
    <changeSet id="*************-140" author="rejam">
        <addForeignKeyConstraint baseColumnNames="tier_id" baseTableName="user_loyalty_account"
                                 constraintName="FK_USER_LOYALTY_ACCOUNT_ON_TIER" referencedColumnNames="id"
                                 referencedTableName="loyalty_tier"/>
    </changeSet>
    <changeSet id="*************-141" author="rejam">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="user_loyalty_account"
                                 constraintName="FK_USER_LOYALTY_ACCOUNT_ON_USER" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-142" author="rejam">
        <addForeignKeyConstraint baseColumnNames="nationality_id" baseTableName="user_passenger"
                                 constraintName="FK_USER_PASSENGER_ON_NATIONALITY" referencedColumnNames="id"
                                 referencedTableName="country"/>
    </changeSet>
    <changeSet id="*************-143" author="rejam">
        <addForeignKeyConstraint baseColumnNames="passport_issuance_country_id" baseTableName="user_passenger"
                                 constraintName="FK_USER_PASSENGER_ON_PASSPORTISSUANCECOUNTRY"
                                 referencedColumnNames="id" referencedTableName="country"/>
    </changeSet>
    <changeSet id="*************-144" author="rejam">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="user_passenger"
                                 constraintName="FK_USER_PASSENGER_ON_USER" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-145" author="rejam">
        <addForeignKeyConstraint baseColumnNames="authority_name" baseTableName="jhi_user_authority"
                                 constraintName="fk_jhiuseaut_on_authority" referencedColumnNames="name"
                                 referencedTableName="jhi_authority"/>
    </changeSet>
    <changeSet id="*************-146" author="rejam">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="jhi_user_authority"
                                 constraintName="fk_jhiuseaut_on_user" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>
    <changeSet id="*************-147" author="rejam">
        <addForeignKeyConstraint baseColumnNames="promo_code_id" baseTableName="rel_promo_code__target_users"
                                 constraintName="fk_relprocodtaruse_on_promo_code" referencedColumnNames="id"
                                 referencedTableName="promo_code"/>
    </changeSet>
    <changeSet id="*************-148" author="rejam">
        <addForeignKeyConstraint baseColumnNames="target_users_id" baseTableName="rel_promo_code__target_users"
                                 constraintName="fk_relprocodtaruse_on_user" referencedColumnNames="id"
                                 referencedTableName="jhi_user"/>
    </changeSet>

</databaseChangeLog>