package com.ferigobooking.api.web.rest.vm;

import jakarta.validation.constraints.NotNull;

/**
 * View Model for Supabase refresh token.
 */
public class SupabaseRefreshTokenVM {

    @NotNull
    private String refreshToken;

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    @Override
    public String toString() {
        return "SupabaseRefreshTokenVM{" + "refreshToken='[PROTECTED]'" + '}';
    }
}
