package com.ferigobooking.api.web.rest;

import com.ferigobooking.api.service.SupabaseAuthService;
import com.ferigobooking.api.web.rest.vm.SupabaseRefreshTokenVM;
import com.ferigobooking.api.web.rest.vm.SupabaseSignInVM;
import com.ferigobooking.api.web.rest.vm.SupabaseSignUpVM;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * REST controller for managing Supabase authentication.
 */
@RestController
@RequestMapping("/api/auth")
public class SupabaseAuthController {

    private static final Logger LOG = LoggerFactory.getLogger(SupabaseAuthController.class);

    private final SupabaseAuthService supabaseAuthService;

    public SupabaseAuthController(SupabaseAuthService supabaseAuthService) {
        this.supabaseAuthService = supabaseAuthService;
    }

    /**
     * {@code POST /auth/signin} : Sign in with email and password.
     */
    @PostMapping("/signin")
    public Mono<ResponseEntity<AuthResponse>> signIn(@Valid @RequestBody SupabaseSignInVM signInVM) {
        LOG.debug("REST request to sign in user: {}", signInVM.getEmail());

        return supabaseAuthService
            .signInWithEmailPassword(signInVM.getEmail(), signInVM.getPassword())
            .flatMap(authTokens ->
                // Sync user data after successful sign in
                supabaseAuthService
                    .getUserFromSupabase(authTokens.getAccessToken())
                    .map(user -> {
                        HttpHeaders httpHeaders = new HttpHeaders();
                        httpHeaders.setBearerAuth(authTokens.getAccessToken());
                        return new ResponseEntity<>(
                            new AuthResponse(authTokens.getAccessToken(), authTokens.getRefreshToken()),
                            httpHeaders,
                            HttpStatus.OK
                        );
                    })
            )
            .onErrorReturn(ResponseEntity.status(HttpStatus.UNAUTHORIZED).build());
    }

    /**
     * {@code POST /auth/signup} : Sign up with email and password.
     */
    @PostMapping("/signup")
    public Mono<ResponseEntity<AuthResponse>> signUp(@Valid @RequestBody SupabaseSignUpVM signUpVM) {
        LOG.debug("REST request to sign up user: {}", signUpVM.getEmail());

        return supabaseAuthService
            .signUpWithEmailPassword(signUpVM.getEmail(), signUpVM.getPassword(), signUpVM.getFirstName(), signUpVM.getLastName())
            .flatMap(authTokens ->
                // Sync user data after successful sign up
                supabaseAuthService
                    .getUserFromSupabase(authTokens.getAccessToken())
                    .map(user -> {
                        HttpHeaders httpHeaders = new HttpHeaders();
                        httpHeaders.setBearerAuth(authTokens.getAccessToken());
                        return new ResponseEntity<>(
                            new AuthResponse(authTokens.getAccessToken(), authTokens.getRefreshToken()),
                            httpHeaders,
                            HttpStatus.CREATED
                        );
                    })
            )
            .onErrorReturn(ResponseEntity.status(HttpStatus.BAD_REQUEST).build());
    }

    /**
     * {@code POST /auth/signout} : Sign out current user.
     */
    @PostMapping("/signout")
    public Mono<ResponseEntity<Void>> signOut(@RequestHeader(HttpHeaders.AUTHORIZATION) String authHeader) {
        LOG.debug("REST request to sign out user");

        String accessToken = authHeader.replace("Bearer ", "");
        return supabaseAuthService
            .signOut(accessToken)
            .then(Mono.just(ResponseEntity.ok().<Void>build()))
            .onErrorReturn(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
    }

    /**
     * {@code POST /auth/refresh} : Refresh access token.
     */
    @PostMapping("/refresh")
    public Mono<ResponseEntity<AuthResponse>> refreshToken(@Valid @RequestBody SupabaseRefreshTokenVM refreshTokenVM) {
        LOG.debug("REST request to refresh token");

        return supabaseAuthService
            .refreshToken(refreshTokenVM.getRefreshToken())
            .map(authTokens -> {
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setBearerAuth(authTokens.getAccessToken());
                return new ResponseEntity<>(
                    new AuthResponse(authTokens.getAccessToken(), authTokens.getRefreshToken()),
                    httpHeaders,
                    HttpStatus.OK
                );
            })
            .onErrorReturn(ResponseEntity.status(HttpStatus.UNAUTHORIZED).build());
    }

    /**
     * Response object for authentication endpoints.
     */
    public static class AuthResponse {

        private final String accessToken;
        private final String refreshToken;

        public AuthResponse(String accessToken, String refreshToken) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }
    }
}
