package com.ferigobooking.api.security;

import com.ferigobooking.api.service.SupabaseAuthService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Filter to automatically sync Supabase users with local database on API calls.
 */
@Component
public class SupabaseJwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger LOG = LoggerFactory.getLogger(SupabaseJwtAuthenticationFilter.class);

    private final SupabaseAuthService supabaseAuthService;

    public SupabaseJwtAuthenticationFilter(SupabaseAuthService supabaseAuthService) {
        this.supabaseAuthService = supabaseAuthService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
        throws ServletException, IOException {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            // Check if user is authenticated with JWT and sync if needed
            if (authentication != null && authentication.getPrincipal() instanceof Jwt jwt) {
                String authHeader = request.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    String accessToken = authHeader.substring(7);

                    // Async user sync - don't block the request
                    supabaseAuthService
                        .getUserFromSupabase(accessToken)
                        .doOnError(error -> LOG.warn("Failed to sync user from Supabase: {}", error.getMessage()))
                        .subscribe(); // Fire and forget
                }
            }
        } catch (Exception e) {
            LOG.warn("Error in Supabase JWT filter: {}", e.getMessage());
        }

        filterChain.doFilter(request, response);
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        // Skip filter for auth endpoints and public endpoints
        return path.startsWith("/api/auth/") || path.startsWith("/management/") || path.startsWith("/v3/api-docs/");
    }
}
