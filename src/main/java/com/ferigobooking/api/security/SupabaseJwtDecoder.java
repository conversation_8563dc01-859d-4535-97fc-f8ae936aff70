package com.ferigobooking.api.security;

import com.ferigobooking.api.config.SupabaseProperties;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import java.text.ParseException;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Component;

/**
 * Custom JWT decoder for Supabase tokens.
 */
@Component
public class SupabaseJwtDecoder implements JwtDecoder {

    private static final Logger LOG = LoggerFactory.getLogger(SupabaseJwtDecoder.class);

    private final SupabaseProperties supabaseProperties;
    private final JWSVerifier verifier;

    public SupabaseJwtDecoder(SupabaseProperties supabaseProperties) throws JOSEException {
        this.supabaseProperties = supabaseProperties;
        this.verifier = new MACVerifier(supabaseProperties.getJwtSecret());
    }

    @Override
    public Jwt decode(String token) throws JwtException {
        try {
            SignedJWT signedJWT = SignedJWT.parse(token);

            // Verify the signature
            if (!signedJWT.verify(verifier)) {
                throw new JwtException("Invalid JWT signature");
            }

            JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();

            // Check expiration
            Date expirationTime = claimsSet.getExpirationTime();
            if (expirationTime != null && expirationTime.before(new Date())) {
                throw new JwtException("JWT token has expired");
            }

            // Convert claims to Spring Security format
            Map<String, Object> claims = claimsSet.getClaims();

            Instant issuedAt = claimsSet.getIssueTime() != null ? claimsSet.getIssueTime().toInstant() : Instant.now();
            Instant expiresAt = expirationTime != null ? expirationTime.toInstant() : null;

            return new Jwt(token, issuedAt, expiresAt, signedJWT.getHeader().toJSONObject(), claims);
        } catch (ParseException | JOSEException e) {
            LOG.error("Failed to parse JWT token", e);
            throw new JwtException("Failed to parse JWT token", e);
        }
    }
}
