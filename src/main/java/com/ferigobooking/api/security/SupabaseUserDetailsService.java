package com.ferigobooking.api.security;

import com.ferigobooking.api.domain.Authority;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.repository.UserRepository;
import com.ferigobooking.api.service.SupabaseAuthService;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Authenticate a user from Supabase and sync with local database.
 */
@Component("supabaseUserDetailsService")
public class SupabaseUserDetailsService implements UserDetailsService {

    private static final Logger LOG = LoggerFactory.getLogger(SupabaseUserDetailsService.class);

    private final UserRepository userRepository;

    public SupabaseUserDetailsService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(final String email) {
        LOG.debug("Authenticating user with email: {}", email);

        Optional<User> userOptional = userRepository.findOneWithAuthoritiesByEmailIgnoreCase(email);

        if (userOptional.isEmpty()) {
            throw new UsernameNotFoundException("User with email " + email + " was not found in the database");
        }

        User user = userOptional.get();
        // All Supabase users are considered activated

        return createSupabaseUserDetails(user);
    }

    private SupabaseUserDetails createSupabaseUserDetails(User user) {
        List<SimpleGrantedAuthority> authorities = user
            .getAuthorities()
            .stream()
            .map(Authority::getName)
            .map(SimpleGrantedAuthority::new)
            .toList();

        return new SupabaseUserDetails(
            user.getEmail(),
            user.getPassword(), // Not used for Supabase auth
            authorities,
            user.getId(),
            user.getFirstName(),
            user.getLastName()
        );
    }

    /**
     * Custom UserDetails implementation for Supabase users.
     */
    public static class SupabaseUserDetails extends org.springframework.security.core.userdetails.User {

        private final Long id;
        private final String firstName;
        private final String lastName;

        public SupabaseUserDetails(
            String email,
            String password,
            Collection<? extends GrantedAuthority> authorities,
            Long id,
            String firstName,
            String lastName
        ) {
            super(email, password, authorities);
            this.id = id;
            this.firstName = firstName;
            this.lastName = lastName;
        }

        public Long getId() {
            return id;
        }

        public String getFirstName() {
            return firstName;
        }

        public String getLastName() {
            return lastName;
        }

        @Override
        public boolean equals(Object obj) {
            return super.equals(obj);
        }

        @Override
        public int hashCode() {
            return super.hashCode();
        }
    }
}
