package com.ferigobooking.api.service.dto;

/**
 * DTO for holding authentication tokens from Supabase.
 */
public class AuthTokensDTO {

    private final String accessToken;
    private final String refreshToken;

    public AuthTokensDTO(String accessToken, String refreshToken) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    @Override
    public String toString() {
        return (
            "AuthTokensDTO{" +
            "accessToken='" +
            (accessToken != null ? accessToken.substring(0, Math.min(50, accessToken.length())) + "..." : "null") +
            '\'' +
            ", refreshToken='" +
            (refreshToken != null ? refreshToken.substring(0, Math.min(50, refreshToken.length())) + "..." : "null") +
            '\'' +
            '}'
        );
    }
}
