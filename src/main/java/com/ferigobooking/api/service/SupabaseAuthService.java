package com.ferigobooking.api.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ferigobooking.api.config.SupabaseProperties;
import com.ferigobooking.api.domain.Authority;
import com.ferigobooking.api.domain.User;
import com.ferigobooking.api.repository.AuthorityRepository;
import com.ferigobooking.api.repository.UserRepository;
import com.ferigobooking.api.security.AuthoritiesConstants;
import com.ferigobooking.api.service.dto.AuthTokensDTO;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * Service for handling Supabase authentication operations.
 */
@Service
@Transactional
public class SupabaseAuthService {

    private static final Logger LOG = LoggerFactory.getLogger(SupabaseAuthService.class);

    private final SupabaseProperties supabaseProperties;
    private final UserRepository userRepository;
    private final AuthorityRepository authorityRepository;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public SupabaseAuthService(
        SupabaseProperties supabaseProperties,
        UserRepository userRepository,
        AuthorityRepository authorityRepository,
        ObjectMapper objectMapper
    ) {
        this.supabaseProperties = supabaseProperties;
        this.userRepository = userRepository;
        this.authorityRepository = authorityRepository;
        this.objectMapper = objectMapper;
        this.webClient = WebClient.builder()
            .baseUrl(supabaseProperties.getUrl())
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader("apikey", supabaseProperties.getAnonKey())
            .build();
    }

    /**
     * Get user information from Supabase and sync with local database.
     */
    public Mono<User> getUserFromSupabase(String accessToken) {
        return webClient
            .get()
            .uri("/auth/v1/user")
            .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
            .retrieve()
            .bodyToMono(String.class)
            .map(this::parseSupabaseUser)
            .map(this::syncUserWithDatabase);
    }

    /**
     * Sign in with email and password using Supabase.
     */
    public Mono<AuthTokensDTO> signInWithEmailPassword(String email, String password) {
        String requestBody = String.format("{\"email\":\"%s\",\"password\":\"%s\"}", email, password);

        return webClient
            .post()
            .uri("/auth/v1/token?grant_type=password")
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(String.class)
            .map(this::extractAuthTokens);
    }

    /**
     * Sign up with email and password using Supabase.
     */
    public Mono<AuthTokensDTO> signUpWithEmailPassword(String email, String password, String firstName, String lastName) {
        String requestBody = String.format(
            "{\"email\":\"%s\",\"password\":\"%s\",\"data\":{\"first_name\":\"%s\",\"last_name\":\"%s\"}}",
            email,
            password,
            firstName != null ? firstName : "",
            lastName != null ? lastName : ""
        );

        return webClient
            .post()
            .uri("/auth/v1/signup")
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(String.class)
            .map(this::extractAuthTokens);
    }

    /**
     * Sign out user from Supabase.
     */
    public Mono<Void> signOut(String accessToken) {
        return webClient
            .post()
            .uri("/auth/v1/logout")
            .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
            .retrieve()
            .bodyToMono(Void.class);
    }

    /**
     * Refresh access token using refresh token.
     */
    public Mono<AuthTokensDTO> refreshToken(String refreshToken) {
        String requestBody = String.format("{\"refresh_token\":\"%s\"}", refreshToken);

        return webClient
            .post()
            .uri("/auth/v1/token?grant_type=refresh_token")
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(String.class)
            .map(this::extractAuthTokens);
    }

    private SupabaseUser parseSupabaseUser(String response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            SupabaseUser user = new SupabaseUser();
            user.setId(jsonNode.get("id").asText());
            user.setEmail(jsonNode.get("email").asText());

            JsonNode userMetadata = jsonNode.get("user_metadata");
            if (userMetadata != null) {
                if (userMetadata.has("first_name")) {
                    user.setFirstName(userMetadata.get("first_name").asText());
                }
                if (userMetadata.has("last_name")) {
                    user.setLastName(userMetadata.get("last_name").asText());
                }
            }

            return user;
        } catch (Exception e) {
            LOG.error("Failed to parse Supabase user response", e);
            throw new RuntimeException("Failed to parse user data", e);
        }
    }

    private AuthTokensDTO extractAuthTokens(String response) {
        try {
            JsonNode jsonNode = objectMapper.readTree(response);
            String accessToken = jsonNode.get("access_token").asText();
            String refreshToken = jsonNode.get("refresh_token").asText();
            return new AuthTokensDTO(accessToken, refreshToken);
        } catch (Exception e) {
            LOG.error("Failed to extract auth tokens from response", e);
            throw new RuntimeException("Failed to extract auth tokens", e);
        }
    }

    private User syncUserWithDatabase(SupabaseUser supabaseUser) {
        // First try to find by Supabase user ID, then by email
        Optional<User> existingUser = userRepository.findOneBySupabaseUserId(supabaseUser.getId());
        if (existingUser.isEmpty()) {
            existingUser = userRepository.findOneByEmailIgnoreCase(supabaseUser.getEmail());
        }

        if (existingUser.isPresent()) {
            User user = existingUser.get();
            // Update user information from Supabase
            user.setSupabaseUserId(supabaseUser.getId());
            user.setEmail(supabaseUser.getEmail().toLowerCase());

            if (supabaseUser.getFirstName() != null) {
                user.setFirstName(supabaseUser.getFirstName());
            }
            if (supabaseUser.getLastName() != null) {
                user.setLastName(supabaseUser.getLastName());
            }
            if (supabaseUser.getPhoneNumber() != null) {
                user.setPhoneNumber(supabaseUser.getPhoneNumber());
            }
            if (supabaseUser.getAvatarUrl() != null) {
                user.setAvatarUrl(supabaseUser.getAvatarUrl());
            }
            if (supabaseUser.getOauthProvider() != null) {
                user.setOauthProvider(supabaseUser.getOauthProvider());
            }
            if (supabaseUser.getOauthProviderId() != null) {
                user.setOauthProviderId(supabaseUser.getOauthProviderId());
            }

            // Update verification status
            user.setEmailVerified(supabaseUser.getEmailVerified());
            user.setPhoneVerified(supabaseUser.getPhoneVerified());

            return userRepository.save(user);
        } else {
            // Create new user
            User newUser = new User();
            newUser.setSupabaseUserId(supabaseUser.getId());
            newUser.setEmail(supabaseUser.getEmail().toLowerCase());
            newUser.setFirstName(supabaseUser.getFirstName());
            newUser.setLastName(supabaseUser.getLastName());
            newUser.setPhoneNumber(supabaseUser.getPhoneNumber());
            newUser.setAvatarUrl(supabaseUser.getAvatarUrl());
            newUser.setOauthProvider(supabaseUser.getOauthProvider());
            newUser.setOauthProviderId(supabaseUser.getOauthProviderId());
            newUser.setEmailVerified(supabaseUser.getEmailVerified());
            newUser.setPhoneVerified(supabaseUser.getPhoneVerified());
            newUser.setLangKey("en");

            // Set default password (not used for Supabase auth)
            newUser.setPassword("$2a$10$VEjxo0jq2YG9Rbk2HmX9S.k1uZBGYUHdUcid3g/vFedUPOdTpuEJG"); // dummy hash

            // Set default authorities
            Set<Authority> authorities = new HashSet<>();
            authorityRepository.findById(AuthoritiesConstants.USER).ifPresent(authorities::add);
            newUser.setAuthorities(authorities);

            return userRepository.save(newUser);
        }
    }

    /**
     * Internal class to represent Supabase user data.
     */
    private static class SupabaseUser {

        private String id;
        private String email;
        private String firstName;
        private String lastName;
        private String phoneNumber;
        private String avatarUrl;
        private String oauthProvider;
        private String oauthProviderId;
        private Boolean emailVerified = false;
        private Boolean phoneVerified = false;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getLastName() {
            return lastName;
        }

        public void setLastName(String lastName) {
            this.lastName = lastName;
        }

        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getOauthProvider() {
            return oauthProvider;
        }

        public void setOauthProvider(String oauthProvider) {
            this.oauthProvider = oauthProvider;
        }

        public String getOauthProviderId() {
            return oauthProviderId;
        }

        public void setOauthProviderId(String oauthProviderId) {
            this.oauthProviderId = oauthProviderId;
        }

        public Boolean getEmailVerified() {
            return emailVerified;
        }

        public void setEmailVerified(Boolean emailVerified) {
            this.emailVerified = emailVerified;
        }

        public Boolean getPhoneVerified() {
            return phoneVerified;
        }

        public void setPhoneVerified(Boolean phoneVerified) {
            this.phoneVerified = phoneVerified;
        }
    }
}
