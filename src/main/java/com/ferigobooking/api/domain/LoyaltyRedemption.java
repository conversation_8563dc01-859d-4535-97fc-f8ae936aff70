package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ferigobooking.api.domain.enumeration.RedemptionStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A LoyaltyRedemption.
 */
@Entity
@Table(name = "loyalty_redemption")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class LoyaltyRedemption implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "loyalty_redemption_seq")
    @SequenceGenerator(name = "loyalty_redemption_seq", sequenceName = "loyalty_redemption_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "redeemed_at", nullable = false)
    private Instant redeemedAt;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private RedemptionStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "authorities", "nationality", "promoCodes" }, allowSetters = true)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    private LoyaltyReward reward;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getRedeemedAt() {
        return this.redeemedAt;
    }

    public void setRedeemedAt(Instant redeemedAt) {
        this.redeemedAt = redeemedAt;
    }

    public RedemptionStatus getStatus() {
        return this.status;
    }

    public void setStatus(RedemptionStatus status) {
        this.status = status;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LoyaltyReward getReward() {
        return this.reward;
    }

    public void setReward(LoyaltyReward loyaltyReward) {
        this.reward = loyaltyReward;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
