package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A FerryPortLookup.
 */
@Entity
@Table(name = "ferry_port_lookup")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class FerryPortLookup implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ferry_port_lookup_seq")
    @SequenceGenerator(name = "ferry_port_lookup_seq", sequenceName = "ferry_port_lookup_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "ref_code")
    private String refCode;

    @ManyToOne(fetch = FetchType.LAZY)
    private Operator operator;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "city" }, allowSetters = true)
    private FerryPort ferryPort;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRefCode() {
        return this.refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }

    public Operator getOperator() {
        return this.operator;
    }

    public void setOperator(Operator operator) {
        this.operator = operator;
    }

    public FerryPort getFerryPort() {
        return this.ferryPort;
    }

    public void setFerryPort(FerryPort ferryPort) {
        this.ferryPort = ferryPort;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here


    // prettier-ignore
    @Override
    public String toString() {
        return "FerryPortLookup{" +
            "id=" + getId() +
            ", refCode='" + getRefCode() + "'" +
            "}";
    }
}
