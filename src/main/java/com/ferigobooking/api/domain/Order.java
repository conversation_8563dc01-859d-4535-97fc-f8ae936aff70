package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ferigobooking.api.domain.enumeration.OrderStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Order.
 */
@Entity
@Table(name = "jhi_order")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Order extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "jhi_order_seq")
    @SequenceGenerator(name = "jhi_order_seq", sequenceName = "jhi_order_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private OrderStatus status;

    @NotNull
    @Column(name = "total_price", precision = 21, scale = 2, nullable = false)
    private BigDecimal totalPrice;

    @NotNull
    @Column(name = "total_discount", precision = 21, scale = 2, nullable = false)
    private BigDecimal totalDiscount;

    @NotNull
    @Column(name = "charged_amount", precision = 21, scale = 2, nullable = false)
    private BigDecimal chargedAmount;

    @NotNull
    @Column(name = "total_fee", precision = 21, scale = 2, nullable = false)
    private BigDecimal totalFee;

    @NotNull
    @Column(name = "organiser_name", nullable = false)
    private String organiserName;

    @NotNull
    @Column(name = "organiser_phone", nullable = false)
    private String organiserPhone;

    @NotNull
    @Column(name = "organiser_email", nullable = false)
    private String organiserEmail;

    @Column(name = "notes")
    private String notes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "authorities", "nationality", "promoCodes" }, allowSetters = true)
    private User customerUserRef;

    @JsonIgnoreProperties(value = { "order", "operator", "organiser" }, allowSetters = true)
    @OneToOne(fetch = FetchType.LAZY, mappedBy = "order")
    private Booking booking;

    @JsonIgnoreProperties(value = { "order" }, allowSetters = true)
    @OneToOne(fetch = FetchType.LAZY, mappedBy = "order")
    private Payment payment;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public OrderStatus getStatus() {
        return this.status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public BigDecimal getTotalPrice() {
        return this.totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getTotalDiscount() {
        return this.totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public BigDecimal getChargedAmount() {
        return this.chargedAmount;
    }

    public void setChargedAmount(BigDecimal chargedAmount) {
        this.chargedAmount = chargedAmount;
    }

    public BigDecimal getTotalFee() {
        return this.totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public String getOrganiserName() {
        return this.organiserName;
    }

    public void setOrganiserName(String organiserName) {
        this.organiserName = organiserName;
    }

    public String getOrganiserPhone() {
        return this.organiserPhone;
    }

    public void setOrganiserPhone(String organiserPhone) {
        this.organiserPhone = organiserPhone;
    }

    public String getOrganiserEmail() {
        return this.organiserEmail;
    }

    public void setOrganiserEmail(String organiserEmail) {
        this.organiserEmail = organiserEmail;
    }

    public String getNotes() {
        return this.notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public User getCustomerUserRef() {
        return this.customerUserRef;
    }

    public void setCustomerUserRef(User user) {
        this.customerUserRef = user;
    }

    public Booking getBooking() {
        return this.booking;
    }

    public void setBooking(Booking booking) {
        if (this.booking != null) {
            this.booking.setOrder(null);
        }
        if (booking != null) {
            booking.setOrder(this);
        }
        this.booking = booking;
    }

    public Payment getPayment() {
        return this.payment;
    }

    public void setPayment(Payment payment) {
        if (this.payment != null) {
            this.payment.setOrder(null);
        }
        if (payment != null) {
            payment.setOrder(this);
        }
        this.payment = payment;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
