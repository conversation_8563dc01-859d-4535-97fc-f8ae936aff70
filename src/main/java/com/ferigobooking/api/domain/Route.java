package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Route.
 */
@Entity
@Table(name = "route")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Route implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "route_seq")
    @SequenceGenerator(name = "route_seq", sequenceName = "route_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Size(min = 6, max = 6)
    @Column(name = "code", length = 6, nullable = false, unique = true)
    private String code;

    @ManyToOne(optional = false)
    @NotNull
    @JsonIgnoreProperties(value = { "city" }, allowSetters = true)
    private FerryPort startPort;

    @ManyToOne(optional = false)
    @NotNull
    @JsonIgnoreProperties(value = { "city" }, allowSetters = true)
    private FerryPort endPort;

    @ManyToOne(fetch = FetchType.LAZY)
    private PortSector sector;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public FerryPort getStartPort() {
        return this.startPort;
    }

    public void setStartPort(FerryPort ferryPort) {
        this.startPort = ferryPort;
    }

    public FerryPort getEndPort() {
        return this.endPort;
    }

    public void setEndPort(FerryPort ferryPort) {
        this.endPort = ferryPort;
    }

    public PortSector getSector() {
        return this.sector;
    }

    public void setSector(PortSector portSector) {
        this.sector = portSector;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
