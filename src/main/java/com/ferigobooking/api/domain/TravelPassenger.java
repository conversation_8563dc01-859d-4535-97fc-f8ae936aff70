package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ferigobooking.api.domain.enumeration.Gender;
import com.ferigobooking.api.domain.enumeration.PassengerType;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A TravelPassenger.
 */
@Entity
@Table(name = "travel_passenger")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TravelPassenger implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "travel_passenger_seq")
    @SequenceGenerator(name = "travel_passenger_seq", sequenceName = "travel_passenger_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "full_name")
    private String fullName;

    @Enumerated(EnumType.STRING)
    @Column(name = "gender")
    private Gender gender;

    @Column(name = "dob")
    private LocalDate dob;

    @Column(name = "passport_no")
    private String passportNo;

    @Column(name = "passport_expiry_date")
    private LocalDate passportExpiryDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private PassengerType type;

    @ManyToOne(fetch = FetchType.LAZY)
    private Country nationality;

    @ManyToOne(fetch = FetchType.LAZY)
    private Country passportIssuanceCountry;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "nationality", "passportIssuanceCountry", "user" }, allowSetters = true)
    private UserPassenger masterPassenger;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "route", "booking" }, allowSetters = true)
    private TripPlan tripPlan;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "route", "operator" }, allowSetters = true)
    private BasePrice fare;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFullName() {
        return this.fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Gender getGender() {
        return this.gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public LocalDate getDob() {
        return this.dob;
    }

    public void setDob(LocalDate dob) {
        this.dob = dob;
    }

    public String getPassportNo() {
        return this.passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public LocalDate getPassportExpiryDate() {
        return this.passportExpiryDate;
    }

    public void setPassportExpiryDate(LocalDate passportExpiryDate) {
        this.passportExpiryDate = passportExpiryDate;
    }

    public PassengerType getType() {
        return this.type;
    }

    public void setType(PassengerType type) {
        this.type = type;
    }

    public Country getNationality() {
        return this.nationality;
    }

    public void setNationality(Country country) {
        this.nationality = country;
    }

    public Country getPassportIssuanceCountry() {
        return this.passportIssuanceCountry;
    }

    public void setPassportIssuanceCountry(Country country) {
        this.passportIssuanceCountry = country;
    }

    public UserPassenger getMasterPassenger() {
        return this.masterPassenger;
    }

    public void setMasterPassenger(UserPassenger userPassenger) {
        this.masterPassenger = userPassenger;
    }

    public TripPlan getTripPlan() {
        return this.tripPlan;
    }

    public void setTripPlan(TripPlan tripPlan) {
        this.tripPlan = tripPlan;
    }

    public BasePrice getFare() {
        return this.fare;
    }

    public void setFare(BasePrice basePrice) {
        this.fare = basePrice;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
