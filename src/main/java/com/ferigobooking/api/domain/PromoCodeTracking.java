package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A PromoCodeTracking.
 */
@Entity
@Table(name = "promo_code_tracking")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromoCodeTracking implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "promo_code_tracking_seq")
    @SequenceGenerator(name = "promo_code_tracking_seq", sequenceName = "promo_code_tracking_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "redeemed_at")
    private Instant redeemedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "customerUserRef", "booking", "payment" }, allowSetters = true)
    private Order order;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "rule", "targetUsers" }, allowSetters = true)
    private PromoCode code;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "authorities", "nationality", "promoCodes" }, allowSetters = true)
    private User redeemedBy;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getRedeemedAt() {
        return this.redeemedAt;
    }

    public void setRedeemedAt(Instant redeemedAt) {
        this.redeemedAt = redeemedAt;
    }

    public Order getOrder() {
        return this.order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public PromoCode getCode() {
        return this.code;
    }

    public void setCode(PromoCode promoCode) {
        this.code = promoCode;
    }

    public User getRedeemedBy() {
        return this.redeemedBy;
    }

    public void setRedeemedBy(User user) {
        this.redeemedBy = user;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
