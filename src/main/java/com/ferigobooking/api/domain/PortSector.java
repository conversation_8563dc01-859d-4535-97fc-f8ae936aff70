package com.ferigobooking.api.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A PortSector.
 */
@Entity
@Table(name = "port_sector")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PortSector implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "port_sector_seq")
    @SequenceGenerator(name = "port_sector_seq", sequenceName = "port_sector_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "code")
    private String code;

    @Column(name = "name")
    private String name;

    @Column(name = "next_sector_code")
    private String nextSectorCode;

    // jhipster-needle-entity-add-field - J<PERSON>ipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNextSectorCode() {
        return this.nextSectorCode;
    }

    public void setNextSectorCode(String nextSectorCode) {
        this.nextSectorCode = nextSectorCode;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
