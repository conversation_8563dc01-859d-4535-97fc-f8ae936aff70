package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ferigobooking.api.domain.enumeration.NotificationChannel;
import com.ferigobooking.api.domain.enumeration.NotificationTriggerEvent;
import com.ferigobooking.api.domain.enumeration.NotificationTriggerType;
import com.ferigobooking.api.domain.enumeration.NotificationType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A NotificationTemplate.
 */
@Entity
@Table(name = "notification_template")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NotificationTemplate extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "notification_template_seq")
    @SequenceGenerator(name = "notification_template_seq", sequenceName = "notification_template_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "code", nullable = false, unique = true)
    private String code;

    @NotNull
    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description")
    private String description;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private NotificationType type;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "trigger_type", nullable = false)
    private NotificationTriggerType triggerType;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "trigger_event", nullable = false)
    private NotificationTriggerEvent triggerEvent;

    @Column(name = "scheduled_time")
    private Instant scheduledTime;

    @Column(name = "recurring_cron_expression")
    private String recurringCronExpression;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "channel", nullable = false)
    private NotificationChannel channel;

    @NotNull
    @Column(name = "title_template", nullable = false)
    private String titleTemplate;

    @Lob
    @Column(name = "body_template", nullable = false)
    private String bodyTemplate;

    @Column(name = "target_url_template")
    private String targetUrlTemplate;

    @NotNull
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public NotificationType getType() {
        return this.type;
    }

    public void setType(NotificationType type) {
        this.type = type;
    }

    public NotificationTriggerType getTriggerType() {
        return this.triggerType;
    }

    public void setTriggerType(NotificationTriggerType triggerType) {
        this.triggerType = triggerType;
    }

    public NotificationTriggerEvent getTriggerEvent() {
        return this.triggerEvent;
    }

    public void setTriggerEvent(NotificationTriggerEvent triggerEvent) {
        this.triggerEvent = triggerEvent;
    }

    public Instant getScheduledTime() {
        return this.scheduledTime;
    }

    public void setScheduledTime(Instant scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    public String getRecurringCronExpression() {
        return this.recurringCronExpression;
    }

    public void setRecurringCronExpression(String recurringCronExpression) {
        this.recurringCronExpression = recurringCronExpression;
    }

    public NotificationChannel getChannel() {
        return this.channel;
    }

    public void setChannel(NotificationChannel channel) {
        this.channel = channel;
    }

    public String getTitleTemplate() {
        return this.titleTemplate;
    }

    public void setTitleTemplate(String titleTemplate) {
        this.titleTemplate = titleTemplate;
    }

    public String getBodyTemplate() {
        return this.bodyTemplate;
    }

    public void setBodyTemplate(String bodyTemplate) {
        this.bodyTemplate = bodyTemplate;
    }

    public String getTargetUrlTemplate() {
        return this.targetUrlTemplate;
    }

    public void setTargetUrlTemplate(String targetUrlTemplate) {
        this.targetUrlTemplate = targetUrlTemplate;
    }

    public Boolean getEnabled() {
        return this.enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
