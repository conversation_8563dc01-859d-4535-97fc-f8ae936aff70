package com.ferigobooking.api.domain;

import com.ferigobooking.api.domain.enumeration.PriceAdjustmentTargetType;
import com.ferigobooking.api.domain.enumeration.PricingComponentType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A PriceAdjustmentRule.
 */
@Entity
@Table(name = "price_adjustment_rule")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PriceAdjustmentRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "price_adjustment_rule_seq")
    @SequenceGenerator(name = "price_adjustment_rule_seq", sequenceName = "price_adjustment_rule_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "name", nullable = false)
    private String name;

    @NotNull
    @Column(name = "name_en", nullable = false)
    private String nameEn;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "adjustment_type", nullable = false)
    private PricingComponentType adjustmentType;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type", nullable = false)
    private PriceAdjustmentTargetType targetType;

    @Lob
    @Column(name = "json_rule_definition", nullable = false)
    private String jsonRuleDefinition;

    @NotNull
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return this.nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public PricingComponentType getAdjustmentType() {
        return this.adjustmentType;
    }

    public void setAdjustmentType(PricingComponentType adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public PriceAdjustmentTargetType getTargetType() {
        return this.targetType;
    }

    public void setTargetType(PriceAdjustmentTargetType targetType) {
        this.targetType = targetType;
    }

    public String getJsonRuleDefinition() {
        return this.jsonRuleDefinition;
    }

    public void setJsonRuleDefinition(String jsonRuleDefinition) {
        this.jsonRuleDefinition = jsonRuleDefinition;
    }

    public Boolean getEnabled() {
        return this.enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
