package com.ferigobooking.api.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Operator.
 */
@Entity
@Table(name = "operator")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Operator implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "operator_seq")
    @SequenceGenerator(name = "operator_seq", sequenceName = "operator_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "logo_url", nullable = false)
    private String logoUrl;

    @NotNull
    @Size(min = 3, max = 3)
    @Column(name = "code", length = 3, nullable = false, unique = true)
    private String code;

    @NotNull
    @Column(name = "name", nullable = false, unique = true)
    private String name;

    @NotNull
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    @NotNull
    @Column(name = "instant_confirmation", nullable = false)
    private Boolean instantConfirmation;

    @Column(name = "open_ticket_expired_at")
    private LocalDate openTicketExpiredAt;

    @Column(name = "open_ticket_expiry_duration_in_months")
    private Integer openTicketExpiryDurationInMonths;

    @NotNull
    @Column(name = "open_ticket_validity_type", nullable = false)
    private String openTicketValidityType;

    @Column(name = "email")
    private String email;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLogoUrl() {
        return this.logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getEnabled() {
        return this.enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getInstantConfirmation() {
        return this.instantConfirmation;
    }

    public void setInstantConfirmation(Boolean instantConfirmation) {
        this.instantConfirmation = instantConfirmation;
    }

    public LocalDate getOpenTicketExpiredAt() {
        return this.openTicketExpiredAt;
    }

    public void setOpenTicketExpiredAt(LocalDate openTicketExpiredAt) {
        this.openTicketExpiredAt = openTicketExpiredAt;
    }

    public Integer getOpenTicketExpiryDurationInMonths() {
        return this.openTicketExpiryDurationInMonths;
    }

    public void setOpenTicketExpiryDurationInMonths(Integer openTicketExpiryDurationInMonths) {
        this.openTicketExpiryDurationInMonths = openTicketExpiryDurationInMonths;
    }

    public String getOpenTicketValidityType() {
        return this.openTicketValidityType;
    }

    public void setOpenTicketValidityType(String openTicketValidityType) {
        this.openTicketValidityType = openTicketValidityType;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
