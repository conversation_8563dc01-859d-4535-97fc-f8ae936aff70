package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A UserLoyaltyAccount.
 */
@Entity
@Table(name = "user_loyalty_account")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class UserLoyaltyAccount extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_loyalty_account_seq")
    @SequenceGenerator(name = "user_loyalty_account_seq", sequenceName = "user_loyalty_account_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "total_points", precision = 21, scale = 2, nullable = false)
    private BigDecimal totalPoints;

    @NotNull
    @Column(name = "available_points", precision = 21, scale = 2, nullable = false)
    private BigDecimal availablePoints;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "authorities", "nationality", "promoCodes" }, allowSetters = true)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    private LoyaltyTier tier;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getTotalPoints() {
        return this.totalPoints;
    }

    public void setTotalPoints(BigDecimal totalPoints) {
        this.totalPoints = totalPoints;
    }

    public BigDecimal getAvailablePoints() {
        return this.availablePoints;
    }

    public void setAvailablePoints(BigDecimal availablePoints) {
        this.availablePoints = availablePoints;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LoyaltyTier getTier() {
        return this.tier;
    }

    public void setTier(LoyaltyTier loyaltyTier) {
        this.tier = loyaltyTier;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
