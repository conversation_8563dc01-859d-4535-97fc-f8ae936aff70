package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ferigobooking.api.domain.enumeration.BookingStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Booking.
 */
@Entity
@Table(name = "booking")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Booking extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "booking_seq")
    @SequenceGenerator(name = "booking_seq", sequenceName = "booking_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "code", nullable = false)
    private String code;

    @NotNull
    @Column(name = "organiser_fullname", nullable = false)
    private String organiserFullname;

    @NotNull
    @Column(name = "organiser_email", nullable = false)
    private String organiserEmail;

    @NotNull
    @Column(name = "organiser_phone", nullable = false)
    private String organiserPhone;

    @NotNull
    @Column(name = "is_round_trip", nullable = false)
    private Boolean isRoundTrip;

    @NotNull
    @Column(name = "is_open_return", nullable = false)
    private Boolean isOpenReturn;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private BookingStatus status;

    @Column(name = "op_confirmation_code")
    private String opConfirmationCode;

    @JsonIgnoreProperties(value = { "customerUserRef", "booking", "payment" }, allowSetters = true)
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(unique = true)
    private Order order;

    @ManyToOne(optional = false)
    @NotNull
    private Operator operator;

    @ManyToOne(fetch = FetchType.LAZY)
    private Organiser organiser;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrganiserFullname() {
        return this.organiserFullname;
    }

    public void setOrganiserFullname(String organiserFullname) {
        this.organiserFullname = organiserFullname;
    }

    public String getOrganiserEmail() {
        return this.organiserEmail;
    }

    public void setOrganiserEmail(String organiserEmail) {
        this.organiserEmail = organiserEmail;
    }

    public String getOrganiserPhone() {
        return this.organiserPhone;
    }

    public void setOrganiserPhone(String organiserPhone) {
        this.organiserPhone = organiserPhone;
    }

    public Boolean getIsRoundTrip() {
        return this.isRoundTrip;
    }

    public void setIsRoundTrip(Boolean isRoundTrip) {
        this.isRoundTrip = isRoundTrip;
    }

    public Boolean getIsOpenReturn() {
        return this.isOpenReturn;
    }

    public void setIsOpenReturn(Boolean isOpenReturn) {
        this.isOpenReturn = isOpenReturn;
    }

    public BookingStatus getStatus() {
        return this.status;
    }

    public void setStatus(BookingStatus status) {
        this.status = status;
    }

    public String getOpConfirmationCode() {
        return this.opConfirmationCode;
    }

    public void setOpConfirmationCode(String opConfirmationCode) {
        this.opConfirmationCode = opConfirmationCode;
    }

    public Order getOrder() {
        return this.order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Operator getOperator() {
        return this.operator;
    }

    public void setOperator(Operator operator) {
        this.operator = operator;
    }

    public Organiser getOrganiser() {
        return this.organiser;
    }

    public void setOrganiser(Organiser organiser) {
        this.organiser = organiser;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
