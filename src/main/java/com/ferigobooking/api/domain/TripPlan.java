package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A TripPlan.
 */
@Entity
@Table(name = "trip_plan")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TripPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "trip_plan_seq")
    @SequenceGenerator(name = "trip_plan_seq", sequenceName = "trip_plan_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @Column(name = "code")
    private String code;

    @NotNull
    @Column(name = "departure_time", nullable = false)
    private ZonedDateTime departureTime;

    @Column(name = "next_trip_code")
    private String nextTripCode;

    @Column(name = "previous_trip_code")
    private String previousTripCode;

    @NotNull
    @Column(name = "duration_in_minutes", nullable = false)
    private Integer durationInMinutes;

    @Column(name = "is_open_trip")
    private Boolean isOpenTrip;

    @Column(name = "open_ticket_expired_at")
    private LocalDate openTicketExpiredAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "startPort", "endPort", "sector" }, allowSetters = true)
    private Route route;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "order", "operator", "organiser" }, allowSetters = true)
    private Booking booking;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ZonedDateTime getDepartureTime() {
        return this.departureTime;
    }

    public void setDepartureTime(ZonedDateTime departureTime) {
        this.departureTime = departureTime;
    }

    public String getNextTripCode() {
        return this.nextTripCode;
    }

    public void setNextTripCode(String nextTripCode) {
        this.nextTripCode = nextTripCode;
    }

    public String getPreviousTripCode() {
        return this.previousTripCode;
    }

    public void setPreviousTripCode(String previousTripCode) {
        this.previousTripCode = previousTripCode;
    }

    public Integer getDurationInMinutes() {
        return this.durationInMinutes;
    }

    public void setDurationInMinutes(Integer durationInMinutes) {
        this.durationInMinutes = durationInMinutes;
    }

    public Boolean getIsOpenTrip() {
        return this.isOpenTrip;
    }

    public void setIsOpenTrip(Boolean isOpenTrip) {
        this.isOpenTrip = isOpenTrip;
    }

    public LocalDate getOpenTicketExpiredAt() {
        return this.openTicketExpiredAt;
    }

    public void setOpenTicketExpiredAt(LocalDate openTicketExpiredAt) {
        this.openTicketExpiredAt = openTicketExpiredAt;
    }

    public Route getRoute() {
        return this.route;
    }

    public void setRoute(Route route) {
        this.route = route;
    }

    public Booking getBooking() {
        return this.booking;
    }

    public void setBooking(Booking booking) {
        this.booking = booking;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
