package com.ferigobooking.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ferigobooking.api.domain.enumeration.LoyaltyTransactionType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A LoyaltyTransaction.
 */
@Entity
@Table(name = "loyalty_transaction")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class LoyaltyTransaction extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "loyalty_transaction_seq")
    @SequenceGenerator(name = "loyalty_transaction_seq", sequenceName = "loyalty_transaction_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private LoyaltyTransactionType type;

    @NotNull
    @Column(name = "points", precision = 21, scale = 2, nullable = false)
    private BigDecimal points;

    @Column(name = "reason")
    private String reason;

    @Column(name = "reference_id")
    private String referenceId;

    @Column(name = "expired_at")
    private Instant expiredAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "authorities", "nationality", "promoCodes" }, allowSetters = true)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "earnRule" }, allowSetters = true)
    private LoyaltyProgram program;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LoyaltyTransactionType getType() {
        return this.type;
    }

    public void setType(LoyaltyTransactionType type) {
        this.type = type;
    }

    public BigDecimal getPoints() {
        return this.points;
    }

    public void setPoints(BigDecimal points) {
        this.points = points;
    }

    public String getReason() {
        return this.reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReferenceId() {
        return this.referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public Instant getExpiredAt() {
        return this.expiredAt;
    }

    public void setExpiredAt(Instant expiredAt) {
        this.expiredAt = expiredAt;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LoyaltyProgram getProgram() {
        return this.program;
    }

    public void setProgram(LoyaltyProgram loyaltyProgram) {
        this.program = loyaltyProgram;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

}
