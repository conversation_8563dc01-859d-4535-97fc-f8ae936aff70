package com.ferigobooking.api.repository;

import com.ferigobooking.api.domain.PromoCode;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

/**
 * Utility repository to load bag relationships based on https://vladmihalcea.com/hibernate-multiplebagfetchexception/
 */
public class PromoCodeRepositoryWithBagRelationshipsImpl implements PromoCodeRepositoryWithBagRelationships {

    private static final String ID_PARAMETER = "id";
    private static final String PROMOCODES_PARAMETER = "promoCodes";

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Optional<PromoCode> fetchBagRelationships(Optional<PromoCode> promoCode) {
        return promoCode.map(this::fetchTargetUsers);
    }

    @Override
    public Page<PromoCode> fetchBagRelationships(Page<PromoCode> promoCodes) {
        return new PageImpl<>(fetchBagRelationships(promoCodes.getContent()), promoCodes.getPageable(), promoCodes.getTotalElements());
    }

    @Override
    public List<PromoCode> fetchBagRelationships(List<PromoCode> promoCodes) {
        return Optional.of(promoCodes).map(this::fetchTargetUsers).orElse(Collections.emptyList());
    }

    PromoCode fetchTargetUsers(PromoCode result) {
        return entityManager
            .createQuery(
                "select promoCode from PromoCode promoCode left join fetch promoCode.targetUsers where promoCode.id = :id",
                PromoCode.class
            )
            .setParameter(ID_PARAMETER, result.getId())
            .getSingleResult();
    }

    List<PromoCode> fetchTargetUsers(List<PromoCode> promoCodes) {
        HashMap<Object, Integer> order = new HashMap<>();
        IntStream.range(0, promoCodes.size()).forEach(index -> order.put(promoCodes.get(index).getId(), index));
        List<PromoCode> result = entityManager
            .createQuery(
                "select promoCode from PromoCode promoCode left join fetch promoCode.targetUsers where promoCode in :promoCodes",
                PromoCode.class
            )
            .setParameter(PROMOCODES_PARAMETER, promoCodes)
            .getResultList();
        Collections.sort(result, (o1, o2) -> Integer.compare(order.get(o1.getId()), order.get(o2.getId())));
        return result;
    }
}
