package com.ferigobooking.api.repository;

import com.ferigobooking.api.domain.PromoCode;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;

public interface PromoCodeRepositoryWithBagRelationships {
    Optional<PromoCode> fetchBagRelationships(Optional<PromoCode> promoCode);

    List<PromoCode> fetchBagRelationships(List<PromoCode> promoCodes);

    Page<PromoCode> fetchBagRelationships(Page<PromoCode> promoCodes);
}
