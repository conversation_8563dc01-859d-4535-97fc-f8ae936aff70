package com.ferigobooking.api.config;

import java.net.URI;
import java.util.concurrent.TimeUnit;
import javax.cache.configuration.MutableConfiguration;
import javax.cache.expiry.CreatedExpiryPolicy;
import javax.cache.expiry.Duration;
import org.hibernate.cache.jcache.ConfigSettings;
import org.redisson.Redisson;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.redisson.jcache.configuration.RedissonConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.JCacheManagerCustomizer;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.boot.info.BuildProperties;
import org.springframework.boot.info.GitProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tech.jhipster.config.JHipsterProperties;
import tech.jhipster.config.cache.PrefixedKeyGenerator;

@Configuration
@EnableCaching
public class CacheConfiguration {

    private GitProperties gitProperties;
    private BuildProperties buildProperties;

    @Bean
    public javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration(JHipsterProperties jHipsterProperties) {
        MutableConfiguration<Object, Object> jcacheConfig = new MutableConfiguration<>();

        URI redisUri = URI.create(jHipsterProperties.getCache().getRedis().getServer()[0]);

        Config config = new Config();
        // Fix Hibernate lazy initialization https://github.com/jhipster/generator-jhipster/issues/22889
        config.setCodec(new org.redisson.codec.SerializationCodec());
        if (jHipsterProperties.getCache().getRedis().isCluster()) {
            ClusterServersConfig clusterServersConfig = config
                .useClusterServers()
                .setMasterConnectionPoolSize(jHipsterProperties.getCache().getRedis().getConnectionPoolSize())
                .setMasterConnectionMinimumIdleSize(jHipsterProperties.getCache().getRedis().getConnectionMinimumIdleSize())
                .setSubscriptionConnectionPoolSize(jHipsterProperties.getCache().getRedis().getSubscriptionConnectionPoolSize())
                .addNodeAddress(jHipsterProperties.getCache().getRedis().getServer());

            if (redisUri.getUserInfo() != null) {
                clusterServersConfig.setPassword(redisUri.getUserInfo().substring(redisUri.getUserInfo().indexOf(':') + 1));
            }
        } else {
            SingleServerConfig singleServerConfig = config
                .useSingleServer()
                .setConnectionPoolSize(jHipsterProperties.getCache().getRedis().getConnectionPoolSize())
                .setConnectionMinimumIdleSize(jHipsterProperties.getCache().getRedis().getConnectionMinimumIdleSize())
                .setSubscriptionConnectionPoolSize(jHipsterProperties.getCache().getRedis().getSubscriptionConnectionPoolSize())
                .setAddress(jHipsterProperties.getCache().getRedis().getServer()[0]);

            if (redisUri.getUserInfo() != null) {
                singleServerConfig.setPassword(redisUri.getUserInfo().substring(redisUri.getUserInfo().indexOf(':') + 1));
            }
        }
        jcacheConfig.setStatisticsEnabled(true);
        jcacheConfig.setExpiryPolicyFactory(
            CreatedExpiryPolicy.factoryOf(new Duration(TimeUnit.SECONDS, jHipsterProperties.getCache().getRedis().getExpiration()))
        );
        return RedissonConfiguration.fromInstance(Redisson.create(config), jcacheConfig);
    }

    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer(javax.cache.CacheManager cm) {
        return hibernateProperties -> hibernateProperties.put(ConfigSettings.CACHE_MANAGER, cm);
    }

    @Bean
    public JCacheManagerCustomizer cacheManagerCustomizer(javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration) {
        return cm -> {
            createCache(cm, com.ferigobooking.api.repository.UserRepository.USERS_BY_EMAIL_CACHE, jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.User.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Authority.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.User.class.getName() + ".authorities", jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.AncillaryProduct.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Booking.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.City.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.CommonLookup.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Country.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.PromoCode.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.PromoCode.class.getName() + ".targetUsers", jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.PromoCodeTracking.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.FerryPort.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.FerryPortLookup.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.UserPassenger.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.BasePrice.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.News.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.NewsCategory.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Operator.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.OperatorRoute.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Order.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.OrderItem.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Organiser.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.TravelPassenger.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Payment.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.PortSector.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.PricingComponent.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Route.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.RouteLookup.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.TripPlan.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.TripSchedule.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.User.class.getName() + ".promoCodes", jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.PriceAdjustmentRule.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.Notification.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.NotificationTemplate.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.LoyaltyProgram.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.LoyaltyRedemption.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.LoyaltyReward.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.LoyaltyRule.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.LoyaltyTransaction.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.UserLoyaltyAccount.class.getName(), jcacheConfiguration);
            createCache(cm, com.ferigobooking.api.domain.LoyaltyTier.class.getName(), jcacheConfiguration);
            // jhipster-needle-redis-add-entry
        };
    }

    private void createCache(
        javax.cache.CacheManager cm,
        String cacheName,
        javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration
    ) {
        javax.cache.Cache<Object, Object> cache = cm.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        } else {
            cm.createCache(cacheName, jcacheConfiguration);
        }
    }

    @Autowired(required = false)
    public void setGitProperties(GitProperties gitProperties) {
        this.gitProperties = gitProperties;
    }

    @Autowired(required = false)
    public void setBuildProperties(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    @Bean
    public KeyGenerator keyGenerator() {
        return new PrefixedKeyGenerator(this.gitProperties, this.buildProperties);
    }
}
